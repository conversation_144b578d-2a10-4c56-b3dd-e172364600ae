# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * oi_payment_allocation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-10-01 12:00+0000\n"
"PO-Revision-Date: 2025-10-01 12:00+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: oi_payment_allocation
#: model:ir.model,name:oi_payment_allocation.model_account_payment_allocation
msgid "Payment Allocation"
msgstr "Allocation de Paiement"

#. module: oi_payment_allocation
#: model:ir.model,name:oi_payment_allocation.model_account_payment_allocation_line
msgid "Payment Allocation Line"
msgstr "Ligne d'Allocation de Paiement"

#. module: oi_payment_allocation
#: model:ir.model,name:oi_payment_allocation.model_account_payment_allocation_writeoff
msgid "Payment Allocation Write off"
msgstr "Écart d'Arrondi Allocation de Paiement"

#. module: oi_payment_allocation
#: selection:account.payment.allocation.line,type:0
msgid "Debit"
msgstr "Débit"

#. module: oi_payment_allocation
#: selection:account.payment.allocation.line,type:0
msgid "Credit"
msgstr "Crédit"

#. module: oi_payment_allocation
#: field:account.payment.allocation,show_child:0
msgid "Show parent/children"
msgstr "Afficher parent/enfants"

#. module: oi_payment_allocation
#: field:account.payment.allocation,writeoff_journal_id:0
msgid "Write off Journal"
msgstr "Journal d'Écart"

#. module: oi_payment_allocation
#: field:account.payment.allocation,writeoff_ref:0
msgid "Write off Reference"
msgstr "Référence d'Écart"

#. module: oi_payment_allocation
#: field:account.payment.allocation,create_entry:0
msgid "Create Account/Partner Entry"
msgstr "Créer Écriture Compte/Partenaire"

#. module: oi_payment_allocation
#: field:account.payment.allocation,entry_journal_id:0
msgid "Account/Partner Entry Journal"
msgstr "Journal Écriture Compte/Partenaire"

#. module: oi_payment_allocation
#: field:account.payment.allocation,entry_name:0
msgid "Entry Reference"
msgstr "Référence Écriture"

#. module: oi_payment_allocation
#: field:account.payment.allocation,ref:0
msgid "Reference"
msgstr "Référence"

#. module: oi_payment_allocation
#: field:account.payment.allocation.line,payment_id:0
msgid "Payment"
msgstr "Paiement"

#. module: oi_payment_allocation
#: field:account.payment.allocation.line,amount_residual_display:0
msgid "Unallocated Amount"
msgstr "Montant Non Alloué"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,name:0
msgid "Label"
msgstr "Libellé"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,balance:0
msgid "Amount"
msgstr "Montant"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,product_id:0
msgid "Product"
msgstr "Produit"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,tax_ids:0
msgid "Taxes"
msgstr "Taxes"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,tax_repartition_line_id:0
msgid "Originator Tax Distribution Line"
msgstr "Ligne de Répartition de Taxe d'Origine"

#. module: oi_payment_allocation
#: field:account.payment.allocation.writeoff,tax_tag_ids:0
msgid "Tags"
msgstr "Étiquettes"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Please use allocation from payment / credit notes"
msgstr "Veuillez utiliser l'allocation à partir du paiement / notes de crédit"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "You are trying to reconcile some entries that are already reconciled."
msgstr "Vous essayez de réconcilier des écritures qui sont déjà réconciliées."

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Account %s does not allow reconciliation. First change the configuration of this account to allow it."
msgstr "Le compte %s n'autorise pas la réconciliation. Modifiez d'abord la configuration de ce compte pour l'autoriser."

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "You can only reconcile posted entries."
msgstr "Vous ne pouvez réconcilier que des écritures validées."

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Entries doesn't belong to the same company: %s != %s"
msgstr "Les écritures n'appartiennent pas à la même société : %s != %s"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Entries are not from the same account: %s != %s"
msgstr "Les écritures ne proviennent pas du même compte : %s != %s"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Write-Off"
msgstr "Écart d'Arrondi"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Select at least one debit line and one credit line"
msgstr "Sélectionnez au moins une ligne de débit et une ligne de crédit"

#. module: oi_payment_allocation
#: code:addons/oi_payment_allocation/wizard/account_payment_allocation.py:0
msgid "Currency exchange rate difference"
msgstr "Écart de change"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Date"
msgstr "Date"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Écriture"
msgstr "Écriture"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Référence"
msgstr "Référence"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Partenaire"
msgstr "Partenaire"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Libellé"
msgstr "Libellé"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Date d'Échéance"
msgstr "Date d'Échéance"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Montant"
msgstr "Montant"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Restant Dû"
msgstr "Restant Dû"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Allouer"
msgstr "Allouer"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Montant Alloué"
msgstr "Montant Alloué"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Solde"
msgstr "Solde"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Séquence"
msgstr "Séquence"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Compte"
msgstr "Compte"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Produit"
msgstr "Produit"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Répartition Analytique"
msgstr "Répartition Analytique"

#. module: oi_payment_allocation
#: view:account.payment.allocation:oi_payment_allocation.view_account_payment_allocation_form
msgid "Taxes"
msgstr "Taxes"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Écriture Débit"
msgstr "Écriture Débit"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Écriture Crédit"
msgstr "Écriture Crédit"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Réconciliation Complète"
msgstr "Réconciliation Complète"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Montant Devise Débit"
msgstr "Montant Devise Débit"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Montant Devise Crédit"
msgstr "Montant Devise Crédit"

#. module: oi_payment_allocation
#: view:account.partial.reconcile:oi_payment_allocation.view_account_partial_reconcile_tree
msgid "Date Maximum"
msgstr "Date Maximum"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Devise Débit"
msgstr "Devise Débit"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Total Montant Devise Débit"
msgstr "Total Montant Devise Débit"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Devise Crédit"
msgstr "Devise Crédit"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Total Montant Devise Crédit"
msgstr "Total Montant Devise Crédit"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Montant Total"
msgstr "Montant Total"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Devise Société"
msgstr "Devise Société"

#. module: oi_payment_allocation
#: view:account.full.reconcile:oi_payment_allocation.view_account_full_reconcile_form
msgid "Écriture de Change"
msgstr "Écriture de Change"