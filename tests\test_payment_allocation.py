"""
Tests unitaires pour le module d'allocation de paiement
"""

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_compare
from unittest.mock import patch
import logging

_logger = logging.getLogger(__name__)


class TestPaymentAllocation(TransactionCase):
    """
    Tests pour le modèle account.payment.allocation
    """
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Configuration de base
        cls.company = cls.env.ref('base.main_company')
        cls.currency_eur = cls.env.ref('base.EUR')
        cls.currency_usd = cls.env.ref('base.USD')
        
        # Comptes comptables
        cls.account_receivable = cls.env['account.account'].create({
            'name': 'Test Receivable',
            'code': 'TEST_REC',
            'account_type': 'asset_receivable',
            'reconcile': True,
            'company_id': cls.company.id,
        })
        
        cls.account_payable = cls.env['account.account'].create({
            'name': 'Test Payable',
            'code': 'TEST_PAY',
            'account_type': 'liability_payable',
            'reconcile': True,
            'company_id': cls.company.id,
        })
        
        cls.account_expense = cls.env['account.account'].create({
            'name': 'Test Expense',
            'code': 'TEST_EXP',
            'account_type': 'expense',
            'company_id': cls.company.id,
        })
        
        # Journaux
        cls.bank_journal = cls.env['account.journal'].create({
            'name': 'Test Bank',
            'code': 'TBANK',
            'type': 'bank',
            'company_id': cls.company.id,
        })
        
        cls.misc_journal = cls.env['account.journal'].create({
            'name': 'Test Miscellaneous',
            'code': 'TMISC',
            'type': 'general',
            'company_id': cls.company.id,
        })
        
        # Partenaires
        cls.partner_customer = cls.env['res.partner'].create({
            'name': 'Test Customer',
            'is_company': True,
            'customer_rank': 1,
        })
        
        cls.partner_vendor = cls.env['res.partner'].create({
            'name': 'Test Vendor',
            'is_company': True,
            'supplier_rank': 1,
        })
    
    def _create_test_invoice(self, partner, amount=1000.0, currency=None):
        """Crée une facture de test"""
        currency = currency or self.currency_eur
        
        invoice = self.env['account.move'].create({
            'move_type': 'out_invoice',
            'partner_id': partner.id,
            'currency_id': currency.id,
            'invoice_line_ids': [(0, 0, {
                'name': 'Test Product',
                'quantity': 1,
                'price_unit': amount,
                'account_id': self.account_expense.id,
            })],
        })
        invoice.action_post()
        return invoice
    
    def _create_test_payment(self, partner, amount=1000.0, currency=None):
        """Crée un paiement de test"""
        currency = currency or self.currency_eur
        
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': partner.id,
            'amount': amount,
            'currency_id': currency.id,
            'journal_id': self.bank_journal.id,
        })
        payment.action_post()
        return payment
    
    def test_allocation_creation_from_payment(self):
        """Test la création d'allocation depuis un paiement"""
        payment = self._create_test_payment(self.partner_customer)
        
        # Simulation du contexte depuis un paiement
        allocation = self.env['account.payment.allocation'].with_context(
            active_model='account.payment',
            active_ids=[payment.id]
        ).create({
            'account_id': self.account_receivable.id,
        })
        
        self.assertEqual(len(allocation.payment_ids), 1)
        self.assertEqual(allocation.payment_ids[0], payment)
        self.assertEqual(allocation.currency_id, self.currency_eur)
    
    def test_allocation_creation_from_invoice(self):
        """Test la création d'allocation depuis une facture"""
        invoice = self._create_test_invoice(self.partner_customer)
        
        # Simulation du contexte depuis une facture
        allocation = self.env['account.payment.allocation'].with_context(
            active_model='account.move',
            active_ids=[invoice.id]
        ).create({
            'account_id': self.account_receivable.id,
        })
        
        self.assertEqual(len(allocation.invoice_ids), 1)
        self.assertEqual(allocation.invoice_ids[0], invoice)
    
    def test_refresh_lines_basic(self):
        """Test l'actualisation des lignes d'allocation"""
        # Crée des données de test
        invoice = self._create_test_invoice(self.partner_customer, 1000.0)
        payment = self._create_test_payment(self.partner_customer, 800.0)
        
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
        })
        
        # Actualise les lignes
        allocation.refresh_lines()
        
        # Vérifie que les lignes sont créées
        self.assertTrue(len(allocation.line_ids) >= 2)
        
        # Vérifie la présence des lignes débit et crédit
        debit_lines = allocation.debit_line_ids
        credit_lines = allocation.credit_line_ids
        
        self.assertTrue(len(debit_lines) > 0)
        self.assertTrue(len(credit_lines) > 0)
    
    def test_balance_calculation(self):
        """Test le calcul du solde"""
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
        })
        
        # Crée des lignes manuellement pour le test
        debit_line = self.env['account.payment.allocation.line'].create({
            'allocation_id': allocation.id,
            'type': 'debit',
            'allocate': True,
            'allocate_amount': 1000.0,
            'move_line_id': self._create_test_invoice(self.partner_customer).line_ids.filtered('debit')[0].id,
        })
        
        credit_line = self.env['account.payment.allocation.line'].create({
            'allocation_id': allocation.id,
            'type': 'credit',
            'allocate': True,
            'allocate_amount': 800.0,
            'move_line_id': self._create_test_payment(self.partner_customer).line_ids.filtered('credit')[0].id,
        })
        
        # Force le recalcul
        allocation._compute_balance()
        
        # Le solde devrait être 1000 - 800 = 200
        self.assertEqual(float_compare(allocation.balance, 200.0, precision_digits=2), 0)
    
    def test_auto_allocate_exact_match(self):
        """Test l'allocation automatique avec correspondance exacte"""
        # Crée une facture et un paiement de même montant
        invoice = self._create_test_invoice(self.partner_customer, 1000.0)
        payment = self._create_test_payment(self.partner_customer, 1000.0)
        
        allocation = self.env['account.payment.allocation'].with_context(
            active_model='account.payment',
            active_ids=[payment.id]
        ).create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
        })
        
        allocation.refresh_lines()
        result = allocation.auto_allocate_lines()
        
        self.assertTrue(result['success'])
        
        # Vérifie que l'allocation est équilibrée
        allocation._compute_balance()
        self.assertEqual(float_compare(allocation.balance, 0.0, precision_digits=2), 0)
    
    def test_auto_allocate_partial_match(self):
        """Test l'allocation automatique avec correspondance partielle"""
        # Crée une facture plus importante qu'un paiement
        invoice = self._create_test_invoice(self.partner_customer, 1500.0)
        payment = self._create_test_payment(self.partner_customer, 1000.0)
        
        allocation = self.env['account.payment.allocation'].with_context(
            active_model='account.payment',
            active_ids=[payment.id]
        ).create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
        })
        
        allocation.refresh_lines()
        result = allocation.auto_allocate_lines()
        
        self.assertTrue(result['success'])
        
        # Vérifie que le solde correspond à la différence
        allocation._compute_balance()
        self.assertEqual(float_compare(allocation.balance, 500.0, precision_digits=2), 0)
    
    def test_validation_without_lines(self):
        """Test la validation sans lignes sélectionnées"""
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
        })
        
        with self.assertRaises(UserError):
            allocation.validate()
    
    def test_validation_with_writeoff(self):
        """Test la validation avec écart d'arrondi"""
        # Crée des données avec un petit écart
        invoice = self._create_test_invoice(self.partner_customer, 1000.0)
        payment = self._create_test_payment(self.partner_customer, 999.5)
        
        allocation = self.env['account.payment.allocation'].with_context(
            active_model='account.payment',
            active_ids=[payment.id]
        ).create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
            'writeoff_journal_id': self.misc_journal.id,
        })
        
        allocation.refresh_lines()
        allocation.auto_allocate_lines()
        
        # Ajoute un écart d'arrondi
        self.env['account.payment.allocation.writeoff'].create({
            'allocation_id': allocation.id,
            'account_id': self.account_expense.id,
            'name': 'Écart de change',
            'balance': 0.5,
        })
        
        # La validation devrait réussir
        result = allocation.validate()
        self.assertEqual(result['type'], 'ir.actions.act_window_close')
    
    def test_multi_currency_allocation(self):
        """Test l'allocation avec plusieurs devises"""
        # Configure un taux de change
        self.env['res.currency.rate'].create({
            'currency_id': self.currency_usd.id,
            'rate': 0.85,  # 1 USD = 0.85 EUR
            'name': '2023-01-01',
            'company_id': self.company.id,
        })
        
        # Crée une facture en EUR et un paiement en USD
        invoice = self._create_test_invoice(self.partner_customer, 1000.0, self.currency_eur)
        payment = self._create_test_payment(self.partner_customer, 1176.47, self.currency_usd)  # ~1000 EUR
        
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
            'currency_id': self.currency_eur.id,
        })
        
        allocation.refresh_lines()
        result = allocation.auto_allocate_lines()
        
        self.assertTrue(result['success'])
        
        # Vérifie que l'allocation est approximativement équilibrée
        allocation._compute_balance()
        self.assertLess(abs(allocation.balance), 1.0)  # Tolérance de 1 EUR
    
    def test_partner_filtering(self):
        """Test le filtrage par partenaire"""
        # Crée des données pour deux partenaires différents
        invoice1 = self._create_test_invoice(self.partner_customer, 1000.0)
        invoice2 = self._create_test_invoice(self.partner_vendor, 500.0)
        
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
        })
        
        allocation.refresh_lines()
        
        # Vérifie que seules les lignes du bon partenaire sont présentes
        partner_ids = allocation.line_ids.mapped('move_line_id.partner_id')
        self.assertIn(self.partner_customer, partner_ids)
        self.assertNotIn(self.partner_vendor, partner_ids)
    
    def test_date_filtering(self):
        """Test le filtrage par dates"""
        # Crée une facture avec une date spécifique
        invoice = self._create_test_invoice(self.partner_customer, 1000.0)
        invoice.write({'date': '2023-01-15'})
        
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
            'date_from': '2023-01-01',
            'date_to': '2023-01-31',
        })
        
        allocation.refresh_lines()
        
        # Vérifie que la facture est incluse
        move_ids = allocation.line_ids.mapped('move_line_id.move_id')
        self.assertIn(invoice, move_ids)
        
        # Test avec des dates qui excluent la facture
        allocation.write({
            'date_from': '2023-02-01',
            'date_to': '2023-02-28',
        })
        
        allocation.refresh_lines()
        
        # Vérifie que la facture est exclue
        move_ids = allocation.line_ids.mapped('move_line_id.move_id')
        self.assertNotIn(invoice, move_ids)
    
    def test_performance_large_dataset(self):
        """Test de performance avec un grand jeu de données"""
        # Crée plusieurs factures et paiements
        invoices = []
        payments = []
        
        for i in range(10):  # Réduit pour les tests
            invoice = self._create_test_invoice(self.partner_customer, 100.0 * (i + 1))
            payment = self._create_test_payment(self.partner_customer, 100.0 * (i + 1))
            invoices.append(invoice)
            payments.append(payment)
        
        allocation = self.env['account.payment.allocation'].create({
            'account_id': self.account_receivable.id,
            'partner_id': self.partner_customer.id,
        })
        
        # Mesure le temps d'actualisation
        import time
        start_time = time.time()
        allocation.refresh_lines()
        refresh_time = time.time() - start_time
        
        # Mesure le temps d'allocation automatique
        start_time = time.time()
        allocation.auto_allocate_lines()
        allocate_time = time.time() - start_time
        
        # Les opérations ne devraient pas prendre plus de quelques secondes
        self.assertLess(refresh_time, 5.0)
        self.assertLess(allocate_time, 5.0)
        
        _logger.info(f"Performance test - Refresh: {refresh_time:.2f}s, Allocate: {allocate_time:.2f}s")
