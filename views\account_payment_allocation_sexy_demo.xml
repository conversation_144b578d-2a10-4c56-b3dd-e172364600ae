<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue de démonstration avec boutons sexy -->
    <record id="view_account_payment_allocation_form_sexy_demo" model="ir.ui.view">
        <field name="name">account.payment.allocation.form.sexy.demo</field>
        <field name="model">account.payment.allocation</field>
        <field name="arch" type="xml">
            <form string="🚀 Allocation de Paiement - Interface Sexy" class="o_payment_allocation_form o_sexy_interface">
                
                <!-- Header avec boutons sexy intégrés -->
                <header class="o_sexy_header">
                    <!-- Boutons principaux sexy -->
                    <div class="o_payment_allocation_header_buttons">
                        <button name="validate" string="🚀 Valider l'Allocation" type="object" 
                                class="btn btn-primary btn-lg o_sexy_btn o_validate_btn" 
                                data-hotkey="v"
                                invisible="balance != 0">
                            <i class="fa fa-check-circle me-2"/>
                            <span class="btn-text">Valider l'Allocation</span>
                        </button>
                        
                        <button name="validate" string="⚠️ Valider avec Écart" type="object" 
                                class="btn btn-warning btn-lg o_sexy_btn o_validate_warning_btn" 
                                data-hotkey="shift+v"
                                invisible="balance == 0">
                            <i class="fa fa-exclamation-triangle me-2"/>
                            <span class="btn-text">Valider avec Écart</span>
                            <small class="btn-subtitle">Solde non équilibré</small>
                        </button>
                        
                        <button name="auto_allocate_lines" string="🎯 Auto-Allocation" type="object" 
                                class="btn btn-success btn-lg o_sexy_btn o_auto_allocate_btn" 
                                data-hotkey="a">
                            <i class="fa fa-magic me-2"/>
                            <span class="btn-text">Auto-Allocation</span>
                            <small class="btn-subtitle">Correspondance intelligente</small>
                        </button>
                        
                        <button name="refresh_lines" string="🔄 Actualiser" type="object" 
                                class="btn btn-info btn-lg o_sexy_btn o_refresh_btn" 
                                data-hotkey="r">
                            <i class="fa fa-refresh me-2"/>
                            <span class="btn-text">Actualiser</span>
                            <small class="btn-subtitle">Recharger les lignes</small>
                        </button>
                        
                        <button name="reset_allocations" string="🗑️ Réinitialiser" type="object" 
                                class="btn btn-secondary btn-lg o_sexy_btn o_reset_btn" 
                                data-hotkey="ctrl+r">
                            <i class="fa fa-undo me-2"/>
                            <span class="btn-text">Réinitialiser</span>
                            <small class="btn-subtitle">Effacer les allocations</small>
                        </button>
                    </div>
                    
                    <!-- Boutons d'action rapide -->
                    <div class="o_payment_allocation_quick_actions">
                        <div class="btn-group" role="group">
                            <button type="object" class="btn btn-outline-primary o_quick_btn" 
                                    name="allocate_oldest_first" 
                                    title="Allouer les plus anciennes en premier">
                                <i class="fa fa-sort-numeric-asc"/>
                                <span class="d-none d-md-inline">📅 Plus Anciennes</span>
                            </button>
                            
                            <button type="object" class="btn btn-outline-success o_quick_btn" 
                                    name="allocate_exact_matches" 
                                    title="Allouer les correspondances exactes">
                                <i class="fa fa-bullseye"/>
                                <span class="d-none d-md-inline">🎯 Exactes</span>
                            </button>
                            
                            <button type="object" class="btn btn-outline-info o_quick_btn" 
                                    name="allocate_by_reference" 
                                    title="Allouer par référence">
                                <i class="fa fa-link"/>
                                <span class="d-none d-md-inline">🔗 Par Réf.</span>
                            </button>
                            
                            <button type="object" class="btn btn-outline-warning o_quick_btn" 
                                    name="get_allocation_preview" 
                                    title="Aperçu de l'allocation">
                                <i class="fa fa-eye"/>
                                <span class="d-none d-md-inline">👁️ Aperçu</span>
                            </button>
                        </div>
                    </div>
                </header>

                <!-- Indicateurs de statut améliorés -->
                <div class="o_payment_allocation_status_enhanced">
                    <div class="alert alert-success" role="alert" invisible="balance != 0">
                        <div class="d-flex align-items-center">
                            <i class="fa fa-check-circle me-3 fa-2x text-success"/>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading mb-1">🎉 Allocation Équilibrée !</h5>
                                <p class="mb-0">Votre allocation est parfaitement équilibrée et prête à être validée.</p>
                            </div>
                            <div class="ms-3">
                                <span class="badge bg-success fs-6">✅ Prêt à valider</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning" role="alert" invisible="balance == 0">
                        <div class="d-flex align-items-center">
                            <i class="fa fa-exclamation-triangle me-3 fa-2x text-warning"/>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading mb-1">⚖️ Solde à Équilibrer</h5>
                                <p class="mb-0">Il reste un écart de <strong field="balance"/> à équilibrer.</p>
                            </div>
                            <div class="ms-3">
                                <span class="badge bg-warning text-dark fs-6">⏳ En cours</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cartes de résumé -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm bg-primary text-white">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2">
                                    <i class="fa fa-arrow-up"/>
                                </div>
                                <h5 class="card-title">💰 Total Débits</h5>
                                <p class="card-text">
                                    <span class="h4" field="debit_total"/>
                                </p>
                                <small class="opacity-75">Montants à recevoir</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm bg-success text-white">
                            <div class="card-body text-center">
                                <div class="display-6 mb-2">
                                    <i class="fa fa-arrow-down"/>
                                </div>
                                <h5 class="card-title">💳 Total Crédits</h5>
                                <p class="card-text">
                                    <span class="h4" field="credit_total"/>
                                </p>
                                <small class="opacity-75">Montants reçus</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm" 
                             style="background: linear-gradient(135deg, #6f42c1, #007bff);">
                            <div class="card-body text-center text-white">
                                <div class="display-6 mb-2">
                                    <i class="fa fa-balance-scale"/>
                                </div>
                                <h5 class="card-title">⚖️ Solde</h5>
                                <p class="card-text">
                                    <span class="h4" field="balance"/>
                                </p>
                                <small class="opacity-75">Différence à équilibrer</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contenu principal avec onglets modernisés -->
                <notebook class="o_sexy_notebook">
                    <page string="💰 Lignes de Débit" name="debit_lines">
                        <field name="debit_line_ids" nolabel="1">
                            <tree decoration-success="allocate == True" 
                                  decoration-muted="allocate == False"
                                  editable="bottom">
                                <field name="allocate" widget="boolean_toggle"/>
                                <field name="document_name"/>
                                <field name="partner_id"/>
                                <field name="date"/>
                                <field name="date_maturity"/>
                                <field name="amount_residual_display" sum="Total"/>
                                <field name="allocate_amount" sum="Alloué"/>
                                <field name="move_currency_id" invisible="1"/>
                            </tree>
                        </field>
                    </page>
                    
                    <page string="💳 Lignes de Crédit" name="credit_lines">
                        <field name="credit_line_ids" nolabel="1">
                            <tree decoration-success="allocate == True" 
                                  decoration-muted="allocate == False"
                                  editable="bottom">
                                <field name="allocate" widget="boolean_toggle"/>
                                <field name="document_name"/>
                                <field name="partner_id"/>
                                <field name="date"/>
                                <field name="date_maturity"/>
                                <field name="amount_residual_display" sum="Total"/>
                                <field name="allocate_amount" sum="Alloué"/>
                                <field name="move_currency_id" invisible="1"/>
                            </tree>
                        </field>
                    </page>
                    
                    <page string="🔧 Écarts d'Arrondi" name="writeoff_lines" 
                          invisible="not writeoff_line_ids">
                        <field name="writeoff_line_ids" nolabel="1">
                            <tree editable="bottom">
                                <field name="account_id"/>
                                <field name="name"/>
                                <field name="balance" sum="Total"/>
                                <field name="analytic_distribution" widget="analytic_distribution"/>
                            </tree>
                        </field>
                    </page>
                </notebook>

                <!-- Informations cachées -->
                <group invisible="1">
                    <field name="account_id"/>
                    <field name="partner_id"/>
                    <field name="currency_id"/>
                    <field name="company_id"/>
                    <field name="writeoff_journal_id"/>
                </group>
                
            </form>
        </field>
    </record>

    <!-- Action pour la vue sexy -->
    <record id="action_account_payment_allocation_sexy" model="ir.actions.act_window">
        <field name="name">🚀 Allocation Sexy de Paiements</field>
        <field name="res_model">account.payment.allocation</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_account_payment_allocation_form_sexy_demo"/>
        <field name="target">new</field>
        <field name="context">{}</field>
    </record>

    <!-- Menu pour accéder à la vue sexy -->
    <menuitem id="menu_account_payment_allocation_sexy"
              name="🚀 Allocation Sexy"
              parent="account.menu_finance_entries_actions"
              action="action_account_payment_allocation_sexy"
              sequence="25"/>

</odoo>
