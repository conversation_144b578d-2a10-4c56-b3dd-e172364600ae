<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Template avec boutons sexy pour l'allocation de paiement -->
    <t t-name="oi_payment_allocation.SexyButtons" owl="1">
        <div class="o_payment_allocation_sexy_interface">
            
            <!-- Boutons principaux sexy -->
            <div class="o_payment_allocation_header_buttons">
                <button type="button" class="btn btn-primary btn-lg o_sexy_btn o_validate_btn" 
                        t-on-click="validateAllocation"
                        t-att-disabled="state.loading or props.readonly"
                        t-att-style="state.isBalanced ? '' : 'display: none;'">
                    <i class="fa fa-check-circle"/>
                    <span class="btn-text">🚀 Valider l'Allocation</span>
                </button>
                
                <button type="button" class="btn btn-warning btn-lg o_sexy_btn o_validate_warning_btn" 
                        t-on-click="validateAllocation"
                        t-att-disabled="state.loading or props.readonly"
                        t-att-style="!state.isBalanced ? '' : 'display: none;'">
                    <i class="fa fa-exclamation-triangle"/>
                    <span class="btn-text">⚠️ Valider avec Écart</span>
                    <small class="btn-subtitle">Solde non équilibré</small>
                </button>
                
                <button type="button" class="btn btn-success btn-lg o_sexy_btn o_auto_allocate_btn" 
                        t-on-click="autoAllocateSmart"
                        t-att-disabled="state.loading or props.readonly">
                    <i class="fa fa-magic"/>
                    <span class="btn-text">🎯 Auto-Allocation</span>
                    <small class="btn-subtitle">Correspondance intelligente</small>
                </button>
                
                <button type="button" class="btn btn-info btn-lg o_sexy_btn o_refresh_btn" 
                        t-on-click="refreshData"
                        t-att-disabled="state.loading">
                    <i class="fa fa-refresh"/>
                    <span class="btn-text">🔄 Actualiser</span>
                    <small class="btn-subtitle">Recharger les lignes</small>
                </button>
                
                <button type="button" class="btn btn-secondary btn-lg o_sexy_btn o_reset_btn" 
                        t-on-click="resetAllocations"
                        t-att-disabled="state.loading or props.readonly">
                    <i class="fa fa-undo"/>
                    <span class="btn-text">🗑️ Réinitialiser</span>
                    <small class="btn-subtitle">Effacer les allocations</small>
                </button>
            </div>
            
            <!-- Boutons d'action rapide -->
            <div class="o_payment_allocation_quick_actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary o_quick_btn" 
                            t-on-click="allocateOldestFirst"
                            t-att-disabled="state.loading or props.readonly"
                            title="Allouer les plus anciennes en premier">
                        <i class="fa fa-sort-numeric-asc"/>
                        <span class="d-none d-md-inline">📅 Plus Anciennes</span>
                    </button>
                    
                    <button type="button" class="btn btn-outline-success o_quick_btn" 
                            t-on-click="allocateExactMatches"
                            t-att-disabled="state.loading or props.readonly"
                            title="Allouer les correspondances exactes">
                        <i class="fa fa-bullseye"/>
                        <span class="d-none d-md-inline">🎯 Exactes</span>
                    </button>
                    
                    <button type="button" class="btn btn-outline-info o_quick_btn" 
                            t-on-click="allocateByReference"
                            t-att-disabled="state.loading or props.readonly"
                            title="Allouer par référence">
                        <i class="fa fa-link"/>
                        <span class="d-none d-md-inline">🔗 Par Réf.</span>
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning o_quick_btn" 
                            t-on-click="showAllocationPreview"
                            t-att-disabled="state.loading"
                            title="Aperçu de l'allocation">
                        <i class="fa fa-eye"/>
                        <span class="d-none d-md-inline">👁️ Aperçu</span>
                    </button>
                </div>
            </div>
            
            <!-- Indicateurs de statut améliorés -->
            <div class="o_payment_allocation_status_enhanced mb-3">
                <div class="alert" t-att-class="getStatusClass()" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <i class="fa fa-info-circle me-2" t-if="!state.isBalanced"/>
                            <i class="fa fa-check-circle me-2" t-if="state.isBalanced"/>
                            <strong t-esc="getStatusMessage()"/>
                        </div>
                        <div class="ms-3">
                            <span class="badge bg-primary me-2">
                                💰 <t t-esc="formatCurrency(state.debitTotal)"/>
                            </span>
                            <span class="badge bg-success me-2">
                                💳 <t t-esc="formatCurrency(state.creditTotal)"/>
                            </span>
                            <span class="badge" t-att-class="state.isBalanced ? 'bg-success' : 'bg-warning'">
                                ⚖️ <t t-esc="formatCurrency(state.balance)"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Barre de progression animée -->
            <div class="o_payment_allocation_progress_enhanced mb-3" t-if="state.debitTotal > 0">
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" 
                         role="progressbar" 
                         t-att-style="'width: ' + Math.min(100, (state.creditTotal / state.debitTotal) * 100) + '%'"
                         t-att-aria-valuenow="Math.min(100, (state.creditTotal / state.debitTotal) * 100)"
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
                <small class="text-muted">
                    Progression de l'allocation : 
                    <strong t-esc="Math.round(Math.min(100, (state.creditTotal / state.debitTotal) * 100))"/>%
                </small>
            </div>
            
            <!-- Raccourcis clavier -->
            <div class="o_payment_allocation_shortcuts" t-if="!props.readonly">
                <small class="text-muted">
                    <i class="fa fa-keyboard-o me-1"/>
                    Raccourcis : 
                    <kbd>Ctrl+A</kbd> Auto-allocation | 
                    <kbd>Ctrl+R</kbd> Réinitialiser | 
                    <kbd>Ctrl+Enter</kbd> Valider |
                    <kbd>Ctrl+Shift+R</kbd> Actualiser
                </small>
            </div>
            
        </div>
    </t>
    
    <!-- Template pour les notifications toast -->
    <t t-name="oi_payment_allocation.ToastNotification" owl="1">
        <div class="toast align-items-center border-0" role="alert" aria-live="assertive" aria-atomic="true"
             t-att-class="'text-bg-' + props.type">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fa" t-att-class="getIconClass()"/>
                    <t t-esc="props.message"/>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </t>
    
    <!-- Template pour les cartes de résumé -->
    <t t-name="oi_payment_allocation.SummaryCard" owl="1">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-6 mb-2" t-att-class="props.colorClass">
                    <i class="fa" t-att-class="props.icon"/>
                </div>
                <h5 class="card-title" t-esc="props.title"/>
                <p class="card-text">
                    <span class="h4" t-att-class="props.colorClass" t-esc="props.value"/>
                </p>
                <small class="text-muted" t-esc="props.subtitle"/>
            </div>
        </div>
    </t>
    
    <!-- Template pour les alertes contextuelles -->
    <t t-name="oi_payment_allocation.ContextAlert" owl="1">
        <div class="alert alert-dismissible fade show" t-att-class="'alert-' + props.type" role="alert">
            <i class="fa me-2" t-att-class="getAlertIcon()"/>
            <strong t-esc="props.title"/>
            <span t-esc="props.message"/>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </t>
    
</templates>
