<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Test des Boutons Sexy - Allocation de Paiements</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Import des styles sexy */
        :root {
            --primary-color: #017e84;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --secondary-color: #6c757d;
            --border-radius: 8px;
            --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Boutons sexy */
        .o_payment_allocation_header_buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .o_sexy_btn {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 160px;
            min-height: 80px;
            padding: 15px 20px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            text-transform: none;
            letter-spacing: 0.3px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            cursor: pointer;
        }

        .o_sexy_btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .o_sexy_btn:hover::before {
            left: 100%;
        }

        .o_sexy_btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .o_sexy_btn .fa {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .o_sexy_btn .btn-text {
            font-size: 14px;
            font-weight: 700;
            line-height: 1.2;
        }

        .o_sexy_btn .btn-subtitle {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 400;
            margin-top: 2px;
            line-height: 1;
        }

        /* Couleurs spécifiques */
        .o_validate_btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .o_validate_warning_btn {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        .o_auto_allocate_btn {
            background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        .o_refresh_btn {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }

        .o_reset_btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
        }

        /* Actions rapides */
        .o_payment_allocation_quick_actions {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .o_quick_btn {
            min-width: 100px;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .o_quick_btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Animation de pulsation */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .o_payment_allocation_header_buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .o_sexy_btn {
                min-width: 100%;
                flex-direction: row;
                justify-content: flex-start;
            }
            
            .o_sexy_btn .fa {
                margin-right: 10px;
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="text-center mb-4">🚀 Démonstration des Boutons Sexy</h1>
        <p class="text-center text-muted mb-5">Interface modernisée pour l'allocation de paiements Odoo</p>

        <!-- Boutons principaux sexy -->
        <div class="o_payment_allocation_header_buttons">
            <button class="btn btn-primary btn-lg o_sexy_btn o_validate_btn" onclick="showDemo('validate')">
                <i class="fa fa-check-circle"></i>
                <span class="btn-text">🚀 Valider l'Allocation</span>
            </button>
            
            <button class="btn btn-warning btn-lg o_sexy_btn o_validate_warning_btn" onclick="showDemo('validate_warning')">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="btn-text">⚠️ Valider avec Écart</span>
                <small class="btn-subtitle">Solde non équilibré</small>
            </button>
            
            <button class="btn btn-success btn-lg o_sexy_btn o_auto_allocate_btn" onclick="showDemo('auto_allocate')">
                <i class="fa fa-magic"></i>
                <span class="btn-text">🎯 Auto-Allocation</span>
                <small class="btn-subtitle">Correspondance intelligente</small>
            </button>
            
            <button class="btn btn-info btn-lg o_sexy_btn o_refresh_btn" onclick="showDemo('refresh')">
                <i class="fa fa-refresh"></i>
                <span class="btn-text">🔄 Actualiser</span>
                <small class="btn-subtitle">Recharger les lignes</small>
            </button>
            
            <button class="btn btn-secondary btn-lg o_sexy_btn o_reset_btn" onclick="showDemo('reset')">
                <i class="fa fa-undo"></i>
                <span class="btn-text">🗑️ Réinitialiser</span>
                <small class="btn-subtitle">Effacer les allocations</small>
            </button>
        </div>
        
        <!-- Boutons d'action rapide -->
        <div class="o_payment_allocation_quick_actions">
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary o_quick_btn" onclick="showDemo('oldest')">
                    <i class="fa fa-sort-numeric-asc"></i>
                    <span>📅 Plus Anciennes</span>
                </button>
                
                <button class="btn btn-outline-success o_quick_btn" onclick="showDemo('exact')">
                    <i class="fa fa-bullseye"></i>
                    <span>🎯 Exactes</span>
                </button>
                
                <button class="btn btn-outline-info o_quick_btn" onclick="showDemo('reference')">
                    <i class="fa fa-link"></i>
                    <span>🔗 Par Réf.</span>
                </button>
                
                <button class="btn btn-outline-warning o_quick_btn" onclick="showDemo('preview')">
                    <i class="fa fa-eye"></i>
                    <span>👁️ Aperçu</span>
                </button>
            </div>
        </div>

        <!-- Zone de démonstration -->
        <div class="mt-4">
            <div class="alert alert-info" role="alert" id="demo-result">
                <h5>🎮 Zone de Démonstration</h5>
                <p>Cliquez sur un bouton pour voir l'effet en action !</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-4">
            <h3>📋 Instructions d'Intégration</h3>
            <ol>
                <li><strong>Redémarrer Odoo</strong> : Redémarrez votre serveur Odoo</li>
                <li><strong>Mettre à jour le module</strong> : Apps → oi_payment_allocation → Mettre à jour</li>
                <li><strong>Vider le cache</strong> : Ctrl+F5 dans votre navigateur</li>
                <li><strong>Tester</strong> : Allez dans Comptabilité → Paiements → Action → Allocation de Paiement</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDemo(action) {
            const resultDiv = document.getElementById('demo-result');
            const messages = {
                'validate': '🚀 <strong>Validation en cours...</strong><br>Création des réconciliations et finalisation de l\'allocation.',
                'validate_warning': '⚠️ <strong>Validation avec écart...</strong><br>Création d\'un écart d\'arrondi pour équilibrer.',
                'auto_allocate': '🎯 <strong>Auto-allocation intelligente...</strong><br>Recherche des correspondances optimales.',
                'refresh': '🔄 <strong>Actualisation...</strong><br>Rechargement de toutes les lignes disponibles.',
                'reset': '🗑️ <strong>Réinitialisation...</strong><br>Effacement de toutes les allocations.',
                'oldest': '📅 <strong>Allocation par ancienneté...</strong><br>Tri par date d\'échéance et allocation chronologique.',
                'exact': '🎯 <strong>Correspondances exactes...</strong><br>Recherche des montants identiques.',
                'reference': '🔗 <strong>Allocation par référence...</strong><br>Correspondance par numéro de référence.',
                'preview': '👁️ <strong>Aperçu de l\'allocation...</strong><br>Génération du résumé détaillé.'
            };

            resultDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div>${messages[action]}</div>
                </div>
            `;

            // Simulation d'une action
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="alert alert-success mb-0">
                        <i class="fa fa-check-circle me-2"></i>
                        <strong>Action terminée avec succès !</strong>
                        L'action "${action}" a été exécutée correctement.
                    </div>
                `;
            }, 2000);
        }

        // Animation de pulsation pour le bouton de validation
        setInterval(() => {
            const validateBtn = document.querySelector('.o_validate_btn');
            validateBtn.classList.add('pulse');
            setTimeout(() => {
                validateBtn.classList.remove('pulse');
            }, 2000);
        }, 5000);
    </script>
</body>
</html>
