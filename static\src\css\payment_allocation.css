/* Styles modernes pour l'allocation de paiement */

.o_payment_allocation_form {
    --primary-color: #017e84;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* En-tête modernisé */
.o_payment_allocation_form .o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.o_payment_allocation_form .oe_title h1 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Indicateurs de statut */
.o_payment_allocation_status {
    margin: 1rem 0;
}

.o_payment_allocation_status .alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    font-weight: 500;
}

.o_payment_allocation_status .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.o_payment_allocation_status .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

/* Groupes de paramètres */
.o_payment_allocation_form .o_group {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.o_payment_allocation_form .o_group .o_group_title {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Champs de formulaire */
.o_payment_allocation_form .o_field_widget input,
.o_payment_allocation_form .o_field_widget select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.o_payment_allocation_form .o_field_widget input:focus,
.o_payment_allocation_form .o_field_widget select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(1, 126, 132, 0.25);
}

/* Notebook modernisé */
.o_payment_allocation_form .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.o_payment_allocation_form .nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.o_payment_allocation_form .nav-tabs .nav-link:hover {
    background-color: var(--light-bg);
    color: var(--primary-color);
}

.o_payment_allocation_form .nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 2px solid var(--primary-color);
}

/* Tables modernisées */
.o_payment_allocation_form .o_list_view {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.o_payment_allocation_form .o_list_view thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #015a5f 100%);
    color: white;
    font-weight: 600;
    padding: 1rem 0.75rem;
    border: none;
}

.o_payment_allocation_form .o_list_view tbody tr {
    transition: all 0.2s ease;
}

.o_payment_allocation_form .o_list_view tbody tr:hover {
    background-color: rgba(1, 126, 132, 0.05);
}

/* Décoration des lignes */
.o_payment_allocation_form .o_list_view tbody tr.text-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.o_payment_allocation_form .o_list_view tbody tr.text-muted {
    background-color: rgba(108, 117, 125, 0.05);
    opacity: 0.7;
}

/* Champs monétaires */
.o_payment_allocation_form .o_field_monetary {
    font-weight: 600;
    font-family: 'Roboto Mono', monospace;
}

.o_payment_allocation_form .o_field_monetary.text-success {
    color: var(--success-color) !important;
}

.o_payment_allocation_form .o_field_monetary.text-danger {
    color: var(--danger-color) !important;
}

/* Toggle switches */
.o_payment_allocation_form .o_field_boolean_toggle {
    transform: scale(1.2);
}

/* Boutons modernisés */
.o_payment_allocation_form .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.o_payment_allocation_form .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #015a5f 100%);
    border: none;
}

.o_payment_allocation_form .btn-primary:hover {
    background: linear-gradient(135deg, #015a5f 0%, #013d41 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.o_payment_allocation_form .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    color: white;
}

.o_payment_allocation_form .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

/* Icônes */
.o_payment_allocation_form .fa {
    margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .o_payment_allocation_form .o_group {
        padding: 1rem;
    }
    
    .o_payment_allocation_form .nav-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .o_payment_allocation_form .o_list_view thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.o_payment_allocation_form .alert {
    animation: fadeIn 0.5s ease-out;
}

/* Tooltips personnalisés */
.o_payment_allocation_form [data-tooltip] {
    position: relative;
    cursor: help;
}

.o_payment_allocation_form [data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
}

/* Indicateurs de progression */
.o_payment_allocation_progress {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin: 1rem 0;
}

.o_payment_allocation_progress .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
    transition: width 0.5s ease;
}

/* ========== BOUTONS SEXY ========== */

/* Container des boutons principaux */
.o_payment_allocation_header_buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Boutons principaux sexy */
.o_sexy_btn {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 160px;
    min-height: 80px;
    padding: 15px 20px;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.3px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    cursor: pointer;
}

.o_sexy_btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.o_sexy_btn:hover::before {
    left: 100%;
}

.o_sexy_btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.o_sexy_btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s;
}

.o_sexy_btn .fa {
    font-size: 24px;
    margin-bottom: 5px;
}

.o_sexy_btn .btn-text {
    font-size: 14px;
    font-weight: 700;
    line-height: 1.2;
}

.o_sexy_btn .btn-subtitle {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 400;
    margin-top: 2px;
    line-height: 1;
}

/* Bouton Valider - Vert success */
.o_validate_btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.o_validate_btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.4);
}

/* Bouton Valider avec écart - Orange warning */
.o_validate_warning_btn {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.o_validate_warning_btn:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e8630a 100%);
    box-shadow: 0 15px 35px rgba(255, 193, 7, 0.4);
}

/* Bouton Auto-allocation - Bleu magique */
.o_auto_allocate_btn {
    background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.o_auto_allocate_btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #5a2d91 100%);
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.4);
}

/* Bouton Actualiser - Cyan info */
.o_refresh_btn {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.o_refresh_btn:hover {
    background: linear-gradient(135deg, #138496 0%, #1ea085 100%);
    box-shadow: 0 15px 35px rgba(23, 162, 184, 0.4);
}

/* Bouton Réinitialiser - Gris secondary */
.o_reset_btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.o_reset_btn:hover {
    background: linear-gradient(135deg, #545b62 0%, #3d4449 100%);
    box-shadow: 0 15px 35px rgba(108, 117, 125, 0.4);
}

/* Actions rapides */
.o_payment_allocation_quick_actions {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.o_quick_btn {
    min-width: 100px;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.o_quick_btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.o_quick_btn:hover::before {
    width: 300px;
    height: 300px;
}

.o_quick_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: currentColor;
}

.o_quick_btn .fa {
    margin-right: 5px;
    font-size: 16px;
}

/* Effets spéciaux pour les boutons rapides */
.btn-outline-primary.o_quick_btn:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-outline-success.o_quick_btn:hover {
    background: linear-gradient(135deg, #28a745, #218838);
    color: white;
}

.btn-outline-info.o_quick_btn:hover {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-outline-warning.o_quick_btn:hover {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

/* Responsive design pour les boutons */
@media (max-width: 768px) {
    .o_payment_allocation_header_buttons {
        flex-direction: column;
        align-items: center;
        gap: 10px;
        padding: 15px;
    }

    .o_sexy_btn {
        min-width: 100%;
        min-height: 60px;
        flex-direction: row;
        justify-content: flex-start;
        padding: 12px 20px;
    }

    .o_sexy_btn .fa {
        font-size: 20px;
        margin-right: 10px;
        margin-bottom: 0;
    }

    .o_sexy_btn .btn-text {
        font-size: 16px;
    }

    .o_sexy_btn .btn-subtitle {
        display: none;
    }

    .o_payment_allocation_quick_actions .btn-group {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
    }

    .o_quick_btn {
        flex: 1;
        min-width: auto;
        margin: 2px;
    }

    .o_quick_btn .d-none.d-md-inline {
        display: none !important;
    }
}

/* Animation de pulsation pour les boutons importants */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.o_validate_btn.pulse {
    animation: pulse 2s infinite;
}

/* Effet de brillance pour les boutons */
.o_sexy_btn.shine::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
