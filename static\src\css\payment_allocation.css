/* Styles modernes pour l'allocation de paiement */

.o_payment_allocation_form {
    --primary-color: #017e84;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* En-tête modernisé */
.o_payment_allocation_form .o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.o_payment_allocation_form .oe_title h1 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Indicateurs de statut */
.o_payment_allocation_status {
    margin: 1rem 0;
}

.o_payment_allocation_status .alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    font-weight: 500;
}

.o_payment_allocation_status .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.o_payment_allocation_status .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

/* Groupes de paramètres */
.o_payment_allocation_form .o_group {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.o_payment_allocation_form .o_group .o_group_title {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Champs de formulaire */
.o_payment_allocation_form .o_field_widget input,
.o_payment_allocation_form .o_field_widget select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.o_payment_allocation_form .o_field_widget input:focus,
.o_payment_allocation_form .o_field_widget select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(1, 126, 132, 0.25);
}

/* Notebook modernisé */
.o_payment_allocation_form .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.o_payment_allocation_form .nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.o_payment_allocation_form .nav-tabs .nav-link:hover {
    background-color: var(--light-bg);
    color: var(--primary-color);
}

.o_payment_allocation_form .nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 2px solid var(--primary-color);
}

/* Tables modernisées */
.o_payment_allocation_form .o_list_view {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.o_payment_allocation_form .o_list_view thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #015a5f 100%);
    color: white;
    font-weight: 600;
    padding: 1rem 0.75rem;
    border: none;
}

.o_payment_allocation_form .o_list_view tbody tr {
    transition: all 0.2s ease;
}

.o_payment_allocation_form .o_list_view tbody tr:hover {
    background-color: rgba(1, 126, 132, 0.05);
}

/* Décoration des lignes */
.o_payment_allocation_form .o_list_view tbody tr.text-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.o_payment_allocation_form .o_list_view tbody tr.text-muted {
    background-color: rgba(108, 117, 125, 0.05);
    opacity: 0.7;
}

/* Champs monétaires */
.o_payment_allocation_form .o_field_monetary {
    font-weight: 600;
    font-family: 'Roboto Mono', monospace;
}

.o_payment_allocation_form .o_field_monetary.text-success {
    color: var(--success-color) !important;
}

.o_payment_allocation_form .o_field_monetary.text-danger {
    color: var(--danger-color) !important;
}

/* Toggle switches */
.o_payment_allocation_form .o_field_boolean_toggle {
    transform: scale(1.2);
}

/* Boutons modernisés */
.o_payment_allocation_form .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.o_payment_allocation_form .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #015a5f 100%);
    border: none;
}

.o_payment_allocation_form .btn-primary:hover {
    background: linear-gradient(135deg, #015a5f 0%, #013d41 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.o_payment_allocation_form .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    color: white;
}

.o_payment_allocation_form .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

/* Icônes */
.o_payment_allocation_form .fa {
    margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .o_payment_allocation_form .o_group {
        padding: 1rem;
    }
    
    .o_payment_allocation_form .nav-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .o_payment_allocation_form .o_list_view thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.o_payment_allocation_form .alert {
    animation: fadeIn 0.5s ease-out;
}

/* Tooltips personnalisés */
.o_payment_allocation_form [data-tooltip] {
    position: relative;
    cursor: help;
}

.o_payment_allocation_form [data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
}

/* Indicateurs de progression */
.o_payment_allocation_progress {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin: 1rem 0;
}

.o_payment_allocation_progress .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--success-color) 100%);
    transition: width 0.5s ease;
}
