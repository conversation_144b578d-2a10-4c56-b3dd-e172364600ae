"""
Modèle d'écart d'arrondi modernisé pour Odoo 17.0
Code refactorisé avec bonnes pratiques et validation renforcée
"""

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.tools import float_is_zero
import logging

_logger = logging.getLogger(__name__)


class PaymentAllocationWriteOffModern(models.TransientModel):
    """
    Écart d'arrondi pour allocation de paiement modernisé
    """
    _name = "account.payment.allocation.writeoff"
    _inherit = ['analytic.mixin']
    _description = 'Écart d\'Arrondi Allocation de Paiement'
    _order = 'sequence, id'
    _check_company_auto = True

    # ========== CHAMPS DE BASE ==========
    
    allocation_id = fields.Many2one(
        'account.payment.allocation',
        string="Allocation",
        required=True,
        ondelete='cascade',
        index=True
    )
    
    sequence = fields.Integer(
        string="Séquence",
        default=10,
        help="Ordre d'affichage des lignes d'écart"
    )
    
    # ========== CHAMPS DE DEVISE ET SOCIÉTÉ ==========
    
    currency_id = fields.Many2one(
        related='allocation_id.currency_id',
        string="Devise",
        store=True
    )
    
    company_id = fields.Many2one(
        related='allocation_id.company_id',
        string="Société",
        store=True
    )
    
    # ========== CHAMPS COMPTABLES ==========
    
    account_id = fields.Many2one(
        'account.account',
        string="Compte",
        required=True,
        domain="[('deprecated', '=', False), "
               "('company_id', '=', company_id), "
               "('account_type', 'not in', ['asset_receivable', 'liability_payable', 'off_balance'])]",
        check_company=True,
        help="Compte comptable pour l'écart d'arrondi"
    )
    
    name = fields.Char(
        string="Libellé",
        required=True,
        help="Description de l'écart d'arrondi"
    )
    
    balance = fields.Monetary(
        string="Montant",
        currency_field='currency_id',
        help="Montant de l'écart d'arrondi"
    )
    
    # ========== CHAMPS PARTENAIRE ET PRODUIT ==========
    
    partner_id = fields.Many2one(
        'res.partner',
        string="Partenaire",
        help="Partenaire associé à l'écart"
    )
    
    product_id = fields.Many2one(
        'product.product',
        string="Produit",
        help="Produit pour la répartition analytique automatique"
    )
    
    # ========== CHAMPS DE TAXES ==========
    
    tax_ids = fields.Many2many(
        'account.tax',
        string="Taxes",
        domain="[('company_id', '=', company_id), ('type_tax_use', '!=', 'none')]",
        help="Taxes applicables à l'écart"
    )
    
    tax_tag_ids = fields.Many2many(
        'account.account.tag',
        string="Étiquettes fiscales",
        help="Étiquettes fiscales pour le reporting"
    )
    
    tax_repartition_line_id = fields.Many2one(
        'account.tax.repartition.line',
        string="Ligne de répartition fiscale",
        help="Ligne de répartition pour les taxes"
    )
    
    auto_tax_line = fields.Boolean(
        string="Ligne de taxe automatique",
        default=False,
        help="Indique si cette ligne est générée automatiquement par les taxes"
    )
    
    # ========== MÉTHODES CALCULÉES ==========
    
    @api.depends('product_id', 'account_id', 'company_id')
    def _compute_analytic_distribution(self):
        """Calcule la répartition analytique basée sur le produit et le compte"""
        for record in self:
            if not record.product_id and not record.account_id:
                record.analytic_distribution = False
                continue
            
            # Recherche du modèle de répartition analytique
            distribution_model = self.env['account.analytic.distribution.model']
            distribution = distribution_model._get_distribution({
                'product_id': record.product_id.id if record.product_id else False,
                'product_categ_id': record.product_id.categ_id.id if record.product_id else False,
                'account_prefix': record.account_id.code if record.account_id else '',
                'company_id': record.company_id.id,
                'partner_id': record.partner_id.id if record.partner_id else False,
            })
            
            record.analytic_distribution = distribution or record.analytic_distribution
    
    # ========== MÉTHODES ONCHANGE ==========
    
    @api.onchange('balance')
    def _onchange_balance(self):
        """Propose automatiquement le solde de l'allocation comme montant d'écart"""
        if not self.balance and self.allocation_id:
            # Propose le solde négatif de l'allocation
            self.balance = -self.allocation_id.balance
    
    @api.onchange('account_id')
    def _onchange_account_id(self):
        """Met à jour le libellé basé sur le compte sélectionné"""
        if self.account_id and not self.name:
            self.name = self.account_id.name
        
        # Réinitialise les taxes si le compte change
        if self.account_id:
            # Récupère les taxes par défaut du compte si configurées
            default_taxes = self.account_id.tax_ids.filtered(
                lambda t: t.company_id == self.company_id
            )
            if default_taxes:
                self.tax_ids = default_taxes
    
    @api.onchange('product_id')
    def _onchange_product_id(self):
        """Met à jour les informations basées sur le produit"""
        if not self.product_id:
            return
        
        # Met à jour le libellé si vide
        if not self.name:
            self.name = self.product_id.display_name
        
        # Met à jour les taxes
        if self.product_id.taxes_id:
            applicable_taxes = self.product_id.taxes_id.filtered(
                lambda t: t.company_id == self.company_id
            )
            if applicable_taxes:
                self.tax_ids = applicable_taxes
        
        # Met à jour le compte si le produit a un compte par défaut
        if not self.account_id:
            if self.balance >= 0:
                # Compte de produit (recette)
                account = (
                    self.product_id.property_account_income_id or
                    self.product_id.categ_id.property_account_income_categ_id
                )
            else:
                # Compte de charge
                account = (
                    self.product_id.property_account_expense_id or
                    self.product_id.categ_id.property_account_expense_categ_id
                )
            
            if account and account.company_id == self.company_id:
                self.account_id = account
    
    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        """Met à jour la répartition analytique basée sur le partenaire"""
        if self.partner_id:
            # Déclenche le recalcul de la répartition analytique
            self._compute_analytic_distribution()
    
    # ========== CONTRAINTES ==========
    
    @api.constrains('balance')
    def _check_balance_not_zero(self):
        """Vérifie que le montant n'est pas nul"""
        for record in self:
            if float_is_zero(record.balance, precision_digits=2):
                raise ValidationError(
                    _("Le montant de l'écart d'arrondi ne peut pas être nul.")
                )
    
    @api.constrains('account_id', 'company_id')
    def _check_account_company(self):
        """Vérifie la cohérence société/compte"""
        for record in self:
            if record.account_id.company_id != record.company_id:
                raise ValidationError(
                    _("Le compte %(account)s n'appartient pas à la société %(company)s") % {
                        'account': record.account_id.display_name,
                        'company': record.company_id.display_name
                    }
                )
    
    @api.constrains('tax_ids', 'company_id')
    def _check_taxes_company(self):
        """Vérifie la cohérence société/taxes"""
        for record in self:
            wrong_taxes = record.tax_ids.filtered(
                lambda t: t.company_id != record.company_id
            )
            if wrong_taxes:
                raise ValidationError(
                    _("Les taxes suivantes n'appartiennent pas à la société %(company)s: %(taxes)s") % {
                        'company': record.company_id.display_name,
                        'taxes': ', '.join(wrong_taxes.mapped('name'))
                    }
                )
    
    # ========== MÉTHODES UTILITAIRES ==========
    
    def get_move_line_values(self):
        """Retourne les valeurs pour créer la ligne d'écriture comptable"""
        self.ensure_one()
        
        values = {
            'account_id': self.account_id.id,
            'name': self.name,
            'partner_id': self.partner_id.id if self.partner_id else False,
            'product_id': self.product_id.id if self.product_id else False,
            'currency_id': self.currency_id.id if self.currency_id != self.company_id.currency_id else False,
            'amount_currency': -self.balance if self.currency_id != self.company_id.currency_id else 0,
            'analytic_distribution': self.analytic_distribution,
            'tax_ids': [(6, 0, self.tax_ids.ids)] if self.tax_ids else [],
            'tax_tag_ids': [(6, 0, self.tax_tag_ids.ids)] if self.tax_tag_ids else [],
            'tax_repartition_line_id': self.tax_repartition_line_id.id if self.tax_repartition_line_id else False,
            'display_type': 'tax' if self.tax_repartition_line_id else 'product'
        }
        
        return values
    
    def suggest_writeoff_amount(self):
        """Suggère un montant d'écart basé sur le solde de l'allocation"""
        self.ensure_one()
        if self.allocation_id:
            suggested_amount = -self.allocation_id.balance
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _("Montant suggéré"),
                    'message': _("Montant suggéré pour équilibrer l'allocation: %s") % suggested_amount,
                    'type': 'info',
                    'sticky': False,
                }
            }
    
    def validate_writeoff_data(self):
        """Valide les données de l'écart d'arrondi"""
        self.ensure_one()
        
        errors = []
        
        # Vérifications de base
        if not self.account_id:
            errors.append(_("Le compte est obligatoire"))
        
        if not self.name:
            errors.append(_("Le libellé est obligatoire"))
        
        if float_is_zero(self.balance, precision_digits=2):
            errors.append(_("Le montant ne peut pas être nul"))
        
        # Vérifications avancées
        if self.account_id and self.account_id.deprecated:
            errors.append(_("Le compte sélectionné est obsolète"))
        
        if self.tax_ids:
            inactive_taxes = self.tax_ids.filtered(lambda t: not t.active)
            if inactive_taxes:
                errors.append(_("Certaines taxes sélectionnées sont inactives"))
        
        if errors:
            raise ValidationError('\n'.join(errors))
        
        return True
    
    @api.model
    def create_from_allocation_balance(self, allocation_id, account_id, name=None):
        """Crée un écart d'arrondi basé sur le solde de l'allocation"""
        allocation = self.env['account.payment.allocation'].browse(allocation_id)
        
        if not allocation.exists():
            raise ValidationError(_("Allocation introuvable"))
        
        if float_is_zero(allocation.balance, precision_digits=2):
            raise ValidationError(_("L'allocation est déjà équilibrée"))
        
        values = {
            'allocation_id': allocation_id,
            'account_id': account_id,
            'name': name or _("Écart d'arrondi - Allocation"),
            'balance': -allocation.balance,
            'partner_id': allocation.partner_id.id if allocation.partner_id else False,
        }
        
        return self.create(values)
