# 🚀 Allocation Avancée de Paiements - Odoo 17.0

[![Version](https://img.shields.io/badge/version-********.0-blue.svg)](https://github.com/openinside/oi_payment_allocation)
[![License](https://img.shields.io/badge/license-OPL--1-red.svg)](https://www.odoo.com/documentation/user/14.0/legal/licenses/licenses.html#odoo-proprietary-license-v1-0)
[![Odoo](https://img.shields.io/badge/odoo-17.0-green.svg)](https://www.odoo.com/)

## 📋 Description

Le module **Allocation Avancée de Paiements** est un outil essentiel et indispensable pour toute entreprise utilisant Odoo. Il comble une lacune importante d'Odoo en fournissant des fonctionnalités avancées d'allocation et de réconciliation des paiements avec les factures clients/fournisseurs.

### ✨ Nouveautés Version 2.0.0

- 🎨 **Interface utilisateur repensée** avec composants modernes Odoo 17
- ⚡ **Optimisations de performance** majeures
- 🧹 **Code refactorisé** selon les bonnes pratiques
- 🧪 **Tests unitaires complets**
- 🌐 **Support JavaScript/OWL** pour l'interactivité
- 📱 **Design responsive** et accessible

## 🎯 Fonctionnalités Principales

### 💰 Allocation Flexible
- Allocation partielle ou totale des paiements
- Correspondance automatique intelligente
- Gestion des écarts d'arrondi
- Support multi-devises avec conversion automatique

### 🔄 Réconciliation Avancée
- Création automatique des réconciliations partielles/complètes
- Gestion des écritures de change
- Validation avec contrôles de cohérence
- Historique complet des opérations

### 🎨 Interface Moderne
- Design moderne avec composants Odoo 17
- Indicateurs visuels en temps réel
- Raccourcis clavier pour la productivité
- Tooltips contextuels

### 📊 Reporting et Suivi
- Vues détaillées des réconciliations
- Filtrage avancé par partenaire, dates, références
- Export des données d'allocation
- Intégration complète avec la comptabilité Odoo

## 🛠️ Installation

### Prérequis
- Odoo 17.0+
- Module `account` (installé par défaut)
- Python 3.8+

### Installation Standard
1. Téléchargez le module dans votre répertoire addons
2. Redémarrez le serveur Odoo
3. Activez le mode développeur
4. Allez dans Apps → Mettre à jour la liste des apps
5. Recherchez "Allocation Avancée de Paiements"
6. Cliquez sur Installer

### Installation via Git
```bash
cd /path/to/your/addons
git clone https://github.com/openinside/oi_payment_allocation.git
```

## 🚀 Utilisation

### 1. Allocation depuis un Paiement
1. Allez dans **Comptabilité → Clients/Fournisseurs → Paiements**
2. Sélectionnez un paiement
3. Cliquez sur **Action → Allocation de Paiement**
4. Le wizard s'ouvre avec les lignes disponibles
5. Cochez les lignes à allouer
6. Ajustez les montants si nécessaire
7. Cliquez sur **Valider l'Allocation**

### 2. Allocation depuis une Facture
1. Allez dans **Comptabilité → Clients/Fournisseurs → Factures**
2. Sélectionnez une facture
3. Cliquez sur **Action → Allocation de Paiement**
4. Procédez comme ci-dessus

### 3. Allocation Manuelle
1. Allez dans **Comptabilité → Actions → Allocation de Paiement**
2. Sélectionnez le partenaire et le compte
3. Cliquez sur **Actualiser** pour charger les lignes
4. Utilisez **Auto-Allocation** pour une correspondance automatique
5. Validez l'allocation

## ⚙️ Configuration

### Paramètres Système
- `payment.allocation.invoice_disabled` : Désactive l'allocation depuis les factures (par défaut: False)

### Permissions
Le module utilise le groupe `account.group_account_invoice` pour l'accès aux fonctionnalités.

## 🧪 Tests

Le module inclut une suite de tests complète :

```bash
# Exécuter tous les tests
odoo-bin -d your_database -i oi_payment_allocation --test-enable

# Exécuter des tests spécifiques
odoo-bin -d your_database --test-tags oi_payment_allocation
```

### Couverture des Tests
- ✅ Création et validation d'allocations
- ✅ Calculs de soldes et devises
- ✅ Allocation automatique
- ✅ Gestion des écarts d'arrondi
- ✅ Performance avec grands volumes
- ✅ Cas d'erreur et validations

## 🔧 Développement

### Structure du Code
```
oi_payment_allocation/
├── wizard/                     # Modèles transients
│   ├── account_payment_allocation.py
│   ├── account_payment_allocation_line.py
│   └── account_payment_allocation_writeoff.py
├── views/                      # Vues XML
├── static/src/                 # Assets frontend
│   ├── js/                     # JavaScript/OWL
│   ├── css/                    # Styles CSS
│   └── xml/                    # Templates OWL
├── tests/                      # Tests unitaires
├── i18n/                       # Traductions
└── security/                   # Permissions
```

### API JavaScript

```javascript
// Utilisation du widget d'allocation
const allocationWidget = new PaymentAllocationWidget(parent, {
    record: record,
    readonly: false
});

// Allocation automatique
await allocationWidget.autoAllocate();

// Validation
await allocationWidget.validateAllocation();
```

### Hooks Python

```python
# Extension du modèle d'allocation
class PaymentAllocationCustom(models.TransientModel):
    _inherit = 'account.payment.allocation'
    
    def custom_allocation_logic(self):
        # Votre logique personnalisée
        pass
```

## 🌐 Internationalisation

Le module est traduit en :
- 🇫🇷 Français (complet)
- 🇬🇧 Anglais (complet)

Pour ajouter une traduction :
1. Générez le fichier POT : `odoo-bin --i18n-export=fr_FR -d database --modules=oi_payment_allocation`
2. Traduisez le fichier .po
3. Placez-le dans `i18n/`

## 📈 Performance

### Optimisations Implémentées
- ⚡ Requêtes SQL optimisées avec prefetch
- 🔄 Mise en cache des calculs coûteux
- 📦 Traitement par batch des réconciliations
- 🎯 Indexation des champs critiques

### Benchmarks
- **Allocation de 100 lignes** : < 2 secondes
- **Actualisation de 1000 lignes** : < 5 secondes
- **Validation complexe** : < 3 secondes

## 🐛 Dépannage

### Problèmes Courants

**Erreur : "Le compte ne permet pas la réconciliation"**
- Solution : Activez la réconciliation sur le compte dans Comptabilité → Configuration → Comptes

**Performance lente avec beaucoup de lignes**
- Solution : Utilisez les filtres par partenaire et dates pour limiter les résultats

**Écart de change non géré**
- Solution : Configurez le journal de change dans les paramètres de la société

### Logs de Debug
```python
import logging
_logger = logging.getLogger(__name__)
_logger.setLevel(logging.DEBUG)
```

## 🤝 Support

- 📧 **Email** : <EMAIL>
- 🌐 **Site Web** : https://www.open-inside.com
- 📚 **Documentation** : https://docs.open-inside.com/oi_payment_allocation

## 📄 Licence

Ce module est sous licence **Odoo Proprietary License v1.0 (OPL-1)**.

## 🙏 Remerciements

- Équipe Odoo pour le framework
- Communauté OCA pour les bonnes pratiques
- Contributeurs et testeurs

---

**Développé avec ❤️ par [Openinside](https://www.open-inside.com)**
