<odoo>

	<record id="view_account_payment_allocation_form" model="ir.ui.view">
		<field name="name">account.payment.allocation.form</field>
		<field name="model">account.payment.allocation</field>
		<field name="arch" type="xml">
			<form string="Allocation de Paiement" class="o_payment_allocation_form">
				<!-- Header avec boutons sexy -->
				<header>
					<div class="o_payment_allocation_header_buttons">
						<button name="validate" string="🚀 Valider l'Allocation" type="object"
								class="btn btn-primary btn-lg o_sexy_btn o_validate_btn"
								data-hotkey="v"
								invisible="balance != 0">
							<i class="fa fa-check-circle me-2"/>
							<span class="btn-text">Valider l'Allocation</span>
						</button>

						<button name="validate" string="⚠️ Valider avec Écart" type="object"
								class="btn btn-warning btn-lg o_sexy_btn o_validate_warning_btn"
								data-hotkey="shift+v"
								invisible="balance == 0">
							<i class="fa fa-exclamation-triangle me-2"/>
							<span class="btn-text">Valider avec Écart</span>
							<small class="btn-subtitle">Solde non équilibré</small>
						</button>

						<button name="auto_allocate_lines" string="🎯 Auto-Allocation" type="object"
								class="btn btn-success btn-lg o_sexy_btn o_auto_allocate_btn"
								data-hotkey="a">
							<i class="fa fa-magic me-2"/>
							<span class="btn-text">Auto-Allocation</span>
							<small class="btn-subtitle">Correspondance intelligente</small>
						</button>

						<button name="refresh_lines" string="🔄 Actualiser" type="object"
								class="btn btn-info btn-lg o_sexy_btn o_refresh_btn"
								data-hotkey="r">
							<i class="fa fa-refresh me-2"/>
							<span class="btn-text">Actualiser</span>
							<small class="btn-subtitle">Recharger les lignes</small>
						</button>

						<button name="reset_allocations" string="🗑️ Réinitialiser" type="object"
								class="btn btn-secondary btn-lg o_sexy_btn o_reset_btn"
								data-hotkey="ctrl+r">
							<i class="fa fa-undo me-2"/>
							<span class="btn-text">Réinitialiser</span>
							<small class="btn-subtitle">Effacer les allocations</small>
						</button>
					</div>

					<!-- Boutons d'action rapide -->
					<div class="o_payment_allocation_quick_actions">
						<div class="btn-group" role="group">
							<button type="object" class="btn btn-outline-primary o_quick_btn"
									name="allocate_oldest_first"
									title="Allouer les plus anciennes en premier">
								<i class="fa fa-sort-numeric-asc"/>
								<span class="d-none d-md-inline">📅 Plus Anciennes</span>
							</button>

							<button type="object" class="btn btn-outline-success o_quick_btn"
									name="allocate_exact_matches"
									title="Allouer les correspondances exactes">
								<i class="fa fa-bullseye"/>
								<span class="d-none d-md-inline">🎯 Exactes</span>
							</button>

							<button type="object" class="btn btn-outline-info o_quick_btn"
									name="allocate_by_reference"
									title="Allouer par référence">
								<i class="fa fa-link"/>
								<span class="d-none d-md-inline">🔗 Par Réf.</span>
							</button>

							<button type="object" class="btn btn-outline-warning o_quick_btn"
									name="get_allocation_preview"
									title="Aperçu de l'allocation">
								<i class="fa fa-eye"/>
								<span class="d-none d-md-inline">👁️ Aperçu</span>
							</button>
						</div>
					</div>
				</header>

				<!-- Indicateurs de statut -->
				<div class="o_payment_allocation_status">
					<div class="alert alert-success" role="alert" invisible="balance != 0">
						<div class="d-flex align-items-center">
							<i class="fa fa-check-circle me-3 fa-2x text-success"/>
							<div class="flex-grow-1">
								<h5 class="alert-heading mb-1">🎉 Allocation Équilibrée !</h5>
								<p class="mb-0">Votre allocation est parfaitement équilibrée et prête à être validée.</p>
							</div>
							<div class="ms-3">
								<span class="badge bg-success fs-6">✅ Prêt à valider</span>
							</div>
						</div>
					</div>

					<div class="alert alert-warning" role="alert" invisible="balance == 0">
						<div class="d-flex align-items-center">
							<i class="fa fa-exclamation-triangle me-3 fa-2x text-warning"/>
							<div class="flex-grow-1">
								<h5 class="alert-heading mb-1">⚖️ Solde à Équilibrer</h5>
								<p class="mb-0">Il reste un écart de <field name="balance" nolabel="1" readonly="1"/> à équilibrer.</p>
							</div>
							<div class="ms-3">
								<span class="badge bg-warning text-dark fs-6">⏳ En cours</span>
							</div>
						</div>
					</div>
				</div>

				<group>
					<group>
						<field name="partner_id" options="{'no_create' : True}"/>
						<field name="show_child" invisible="not partner_id"/>
						<field name="payment_ids" invisible="1"/>
						<field name="invoice_ids" invisible="1"/>
						<field name="move_line_ids" invisible="1"/>
						<field name="active_move_line_ids" invisible="1"/>
						<field name="currency_id" options="{'no_create' : True, 'no_open' : 1}" groups="base.group_multi_currency"/>
					</group>
					<group>
						<field name="company_id"/>
						<field name="account_id" options="{'no_create' : True}" domain="[('company_id', '=', company_id), ('reconcile','=', True)]"/>
						<label for="date_from" string="Date"/>
						<div class="o_row">
							<field name="date_from" nolabel="1" class="oe_inline"/>
							<field name="date_to" nolabel="1" class="oe_inline"/>
						</div>
						<field name="ref"/>
					</group>
					<group>
						<field name="balance" readonly="1" string="Solde"/>
						<field name="debit_total" readonly="1" string="Total Débits"/>
						<field name="credit_total" readonly="1" string="Total Crédits"/>
					</group>
				</group>
				<group string="Débit">
					<field name="debit_line_ids" nolabel="1" colspan="2">
						<tree editable="bottom" create="false" delete="false">
							<field name="move_line_id" column_invisible="1"/>
							<field name="type" column_invisible="1"/>
							<field name="amount_residual" column_invisible="1"/>
							<field name="sign" column_invisible="1"/>
							<field name="company_currency_id" column_invisible="1"/>
							<field name="allocation_currency_id" column_invisible="1"/>
							<field name="move_currency_id" column_invisible="1"/>							
							<field name="move_id" column_invisible="1"/>
							<field name="move_name" string="Écriture"/>
							<field name="ref" optional="show" string="Référence"/>
							<field name="partner_id" optional="show" column_invisible="not parent.show_child and parent.partner_id" string="Partenaire"/>
							<field name="name" optional="hide" string="Libellé"/>
							<field name="date" optional="hide" string="Date"/>
							<field name="date_maturity" optional="show" string="Date d'Échéance"/>
							<field name="amount" optional="show" string="Montant"/>
							<field name="amount_residual_display" string="Restant Dû"/>
							<field name="allocate" widget="boolean_toggle" options="{'autosave' : False}" string="Allouer"/>
							<field name="allocate_amount" sum="Total" readonly="not allocate" string="Montant Alloué"/>
						</tree>
					</field>
				</group>
				<group string="Crédit">
					<field name="credit_line_ids" nolabel="1" colspan="2">
						<tree editable="bottom" create="false" delete="false">
							<field name="move_line_id" column_invisible="1"/>
							<field name="type" column_invisible="1"/>
							<field name="amount_residual" column_invisible="1"/>
							<field name="sign" column_invisible="1"/>
							<field name="company_currency_id" column_invisible="1"/>
							<field name="allocation_currency_id" column_invisible="1"/>
							<field name="move_currency_id" column_invisible="1"/>							
							<field name="move_id" column_invisible="1"/>
							<field name="move_name" string="Écriture"/>
							<field name="ref" optional="show" string="Référence"/>
							<field name="partner_id" optional="show" column_invisible="not parent.show_child and parent.partner_id" string="Partenaire"/>
							<field name="name" optional="hide" string="Libellé"/>
							<field name="date" optional="hide" string="Date"/>
							<field name="date_maturity" optional="show" string="Date d'Échéance"/>
							<field name="amount" optional="show" string="Montant"/>
							<field name="amount_residual_display" string="Restant Dû"/>
							<field name="allocate" widget="boolean_toggle" options="{'autosave' : False}" string="Allouer"/>
							<field name="allocate_amount" sum="Total" readonly="not allocate" string="Montant Alloué"/>
						</tree>
					</field>
				</group>
				<group>
					<group>
						<field name="balance" string="Solde"/>
					</group>
					<group string="Écriture Compte/Partenaire" invisible="not show_child or not partner_id">
						<field name="create_entry" string="Créer une Écriture"/>
						<field name="entry_journal_id" string="Journal" domain="[('company_id', '=', company_id)]" options="{'no_create' : True}" required="show_child and create_entry"/>
						<field name="entry_name"/>
					</group>					
				</group>
				<group string="Écart d'Arrondi" invisible="balance == 0 and writeoff_line_ids == []">
					<field name="writeoff_journal_id" string="Journal" domain="[('company_id', '=', company_id)]" options="{'no_create_edit' : True}"/>
					<field name="writeoff_ref" string="Référence" invisible="not writeoff_journal_id"/>
					<field name="writeoff_line_ids" colspan="2" nolabel="1" invisible="not writeoff_journal_id">
						<tree editable="bottom">
							<field name="sequence" widget="handle" string="Séquence"/>
							<field name="name" string="Libellé"/>
							<field name="account_id" string="Compte"/>
							<field name="balance" force_save="1" string="Montant"/>
							<field name="partner_id" optional="show" string="Partenaire"/>
							<field name="product_id" optional="hide" string="Produit"/>
							<field name="analytic_distribution" optional="show" widget="analytic_distribution" groups="analytic.group_analytic_accounting" string="Répartition Analytique"/>
							<field name="tax_ids" context="{'append_type_to_tax_name': True}" widget="many2many_tags" optional="show" options="{'no_create' : 1}" string="Taxes"/>
							<field name="company_id" column_invisible="1"/>
							<field name="currency_id" column_invisible="1"/>
							<field name="auto_tax_line" column_invisible="1"/>
							<field name="tax_tag_ids" column_invisible="1"/>
							<field name="tax_repartition_line_id" column_invisible="1"/>
						</tree>
						<form>
							<field name="name" string="Libellé"/>
							<field name="account_id" string="Compte"/>
							<field name="balance" string="Montant"/>
							<field name="partner_id" string="Partenaire"/>
							<field name="product_id" string="Produit"/>
							<field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting" string="Répartition Analytique"/>
							<field name="tax_ids" widget="many2many_tags" string="Taxes"/>
							<field name="company_id" column_invisible="1"/>
							<field name="currency_id" column_invisible="1"/>
							<field name="tax_repartition_line_id" column_invisible="1"/>
							<field name="auto_tax_line" column_invisible="1"/>
						</form>
					</field>
				</group>

				<footer>
					<button string="Valider" name="validate" type="object" class="btn btn-primary"/>
					<button string="Annuler" class="btn btn-default" special="cancel"/>
				</footer>
			</form>
		</field>
	</record>


</odoo>
