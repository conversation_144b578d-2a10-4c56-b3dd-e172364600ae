<odoo>

	<record id="view_account_payment_allocation_form" model="ir.ui.view">
		<field name="name">account.payment.allocation.form</field>
		<field name="model">account.payment.allocation</field>
		<field name="arch" type="xml">
			<form>
				<group>
					<group>
						<field name="partner_id" options="{'no_create' : True}"/>
						<field name="show_child" invisible="not partner_id"/>
						<field name="payment_ids" invisible="1"/>
						<field name="invoice_ids" invisible="1"/>
						<field name="move_line_ids" invisible="1"/>
						<field name="active_move_line_ids" invisible="1"/>
						<field name="currency_id" options="{'no_create' : True, 'no_open' : 1}" groups="base.group_multi_currency"/>
					</group>
					<group>
						<field name="company_id"/>
						<field name="account_id" options="{'no_create' : True}" domain="[('company_id', '=', company_id), ('reconcile','=', True)]"/>
						<label for="date_from" string="Date"/>
						<div class="o_row">
							<field name="date_from" nolabel="1" class="oe_inline"/>
							<field name="date_to" nolabel="1" class="oe_inline"/>
						</div>
						<field name="ref"/>
					</group>
				</group>
				<group string="Débit">
					<field name="debit_line_ids" nolabel="1" colspan="2">
						<tree editable="bottom" create="false" delete="false">
							<field name="move_line_id" column_invisible="1"/>
							<field name="type" column_invisible="1"/>
							<field name="amount_residual" column_invisible="1"/>
							<field name="sign" column_invisible="1"/>
							<field name="company_currency_id" column_invisible="1"/>
							<field name="allocation_currency_id" column_invisible="1"/>
							<field name="move_currency_id" column_invisible="1"/>							
							<field name="move_id" column_invisible="1"/>
							<field name="move_name" string="Écriture"/>
							<field name="ref" optional="show" string="Référence"/>
							<field name="partner_id" optional="show" column_invisible="not parent.show_child and parent.partner_id" string="Partenaire"/>
							<field name="name" optional="hide" string="Libellé"/>
							<field name="date" optional="hide" string="Date"/>
							<field name="date_maturity" optional="show" string="Date d'Échéance"/>
							<field name="amount" optional="show" string="Montant"/>
							<field name="amount_residual_display" string="Restant Dû"/>
							<field name="allocate" widget="boolean_toggle" options="{'autosave' : False}" string="Allouer"/>
							<field name="allocate_amount" sum="Total" readonly="not allocate" string="Montant Alloué"/>
						</tree>
					</field>
				</group>
				<group string="Crédit">
					<field name="credit_line_ids" nolabel="1" colspan="2">
						<tree editable="bottom" create="false" delete="false">
							<field name="move_line_id" column_invisible="1"/>
							<field name="type" column_invisible="1"/>
							<field name="amount_residual" column_invisible="1"/>
							<field name="sign" column_invisible="1"/>
							<field name="company_currency_id" column_invisible="1"/>
							<field name="allocation_currency_id" column_invisible="1"/>
							<field name="move_currency_id" column_invisible="1"/>							
							<field name="move_id" column_invisible="1"/>
							<field name="move_name" string="Écriture"/>
							<field name="ref" optional="show" string="Référence"/>
							<field name="partner_id" optional="show" column_invisible="not parent.show_child and parent.partner_id" string="Partenaire"/>
							<field name="name" optional="hide" string="Libellé"/>
							<field name="date" optional="hide" string="Date"/>
							<field name="date_maturity" optional="show" string="Date d'Échéance"/>
							<field name="amount" optional="show" string="Montant"/>
							<field name="amount_residual_display" string="Restant Dû"/>
							<field name="allocate" widget="boolean_toggle" options="{'autosave' : False}" string="Allouer"/>
							<field name="allocate_amount" sum="Total" readonly="not allocate" string="Montant Alloué"/>
						</tree>
					</field>
				</group>
				<group>
					<group>
						<field name="balance" string="Solde"/>
					</group>
					<group string="Écriture Compte/Partenaire" invisible="not show_child or not partner_id">
						<field name="create_entry" string="Créer une Écriture"/>
						<field name="entry_journal_id" string="Journal" domain="[('company_id', '=', company_id)]" options="{'no_create' : True}" required="show_child and create_entry"/>
						<field name="entry_name"/>
					</group>					
				</group>
				<group string="Écart d'Arrondi" invisible="balance == 0 and writeoff_line_ids == []">
					<field name="writeoff_journal_id" string="Journal" domain="[('company_id', '=', company_id)]" options="{'no_create_edit' : True}"/>
					<field name="writeoff_ref" string="Référence" invisible="not writeoff_journal_id"/>
					<field name="writeoff_line_ids" colspan="2" nolabel="1" invisible="not writeoff_journal_id">
						<tree editable="bottom">
							<field name="sequence" widget="handle" string="Séquence"/>
							<field name="name" string="Libellé"/>
							<field name="account_id" string="Compte"/>
							<field name="balance" force_save="1" string="Montant"/>
							<field name="partner_id" optional="show" string="Partenaire"/>
							<field name="product_id" optional="hide" string="Produit"/>
							<field name="analytic_distribution" optional="show" widget="analytic_distribution" groups="analytic.group_analytic_accounting" string="Répartition Analytique"/>
							<field name="tax_ids" context="{'append_type_to_tax_name': True}" widget="many2many_tags" optional="show" options="{'no_create' : 1}" string="Taxes"/>
							<field name="company_id" column_invisible="1"/>
							<field name="currency_id" column_invisible="1"/>
							<field name="auto_tax_line" column_invisible="1"/>
							<field name="tax_tag_ids" column_invisible="1"/>
							<field name="tax_repartition_line_id" column_invisible="1"/>
						</tree>
						<form>
							<field name="name" string="Libellé"/>
							<field name="account_id" string="Compte"/>
							<field name="balance" string="Montant"/>
							<field name="partner_id" string="Partenaire"/>
							<field name="product_id" string="Produit"/>
							<field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting" string="Répartition Analytique"/>
							<field name="tax_ids" widget="many2many_tags" string="Taxes"/>
							<field name="company_id" column_invisible="1"/>
							<field name="currency_id" column_invisible="1"/>
							<field name="tax_repartition_line_id" column_invisible="1"/>
							<field name="auto_tax_line" column_invisible="1"/>
						</form>
					</field>
				</group>

				<footer>
					<button string="Valider" name="validate" type="object" class="btn btn-primary"/>
					<button string="Annuler" class="btn btn-default" special="cancel"/>
				</footer>
			</form>
		</field>
	</record>


</odoo>
