'''
Created on Nov 27, 2022

@author: <PERSON><PERSON><PERSON>
'''
from odoo import models, fields, api

class PaymentAllocationWriteOff(models.TransientModel):
    _name = "account.payment.allocation.writeoff"
    _inherit = ['analytic.mixin']    
    _description ='Écart d\'Arrondi Allocation de Paiement'
    _order = 'sequence,id'
    
    allocation_id = fields.Many2one('account.payment.allocation', required = True, ondelete='cascade')
    sequence = fields.Integer()
    currency_id = fields.Many2one(related='allocation_id.currency_id')
    company_id = fields.Many2one(related='allocation_id.company_id')        
    account_id = fields.Many2one('account.account', required = True, domain="[('deprecated', '=', False), ('company_id', '=', company_id),('account_type','not in', ['asset_receivable','liability_payable','off_balance'])]", check_company=True)
    name = fields.Char(string='Libellé')
    balance = fields.Monetary('Montant')
    partner_id = fields.Many2one('res.partner')
    product_id = fields.Many2one('product.product', string='Produit')
    
    tax_ids = fields.Many2many('account.tax', string='Taxes', help="Taxes qui s'appliquent sur le montant de base", check_company=True)
    tax_repartition_line_id = fields.Many2one(comodel_name='account.tax.repartition.line',
        string="Ligne de Répartition de Taxe d'Origine", ondelete='restrict', readonly=True,
        check_company=True,
        help="Ligne de répartition de taxe qui a causé la création de cette ligne d'écriture, le cas échéant")    
    tax_tag_ids = fields.Many2many(string="Étiquettes", comodel_name='account.account.tag', ondelete='restrict',
        help="Étiquettes attribuées à cette ligne par la taxe qui l'a créée, le cas échéant. Cela détermine son impact sur les rapports financiers.")
    
    auto_tax_line = fields.Boolean()
            
    
    @api.depends('product_id', 'account_id')
    def _compute_analytic_distribution2(self):
        for record in self:
            distribution = self.env['account.analytic.distribution.model']._get_distribution({
                'product_id': record.product_id.id,
                'product_categ_id': record.product_id.categ_id.id,
                'account_prefix': record.account_id.code,
                'company_id': record.company_id.id,
            })
            record.analytic_distribution = distribution or record.analytic_distribution
    
                                                                                            
    @api.onchange('balance')
    def _onchange_statement_line_id(self):
        if not self.balance:
            self.balance = -self.allocation_id.balance            

    @api.onchange('account_id')
    def _onchange_account_id(self):
        if self.account_id and not self.name:
            self.name = self.account_id.name
    