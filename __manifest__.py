# -*- coding: utf-8 -*-
{
    'name': 'Allocation Avancée de Paiements / Réconciliation',
    'summary': 'Allocation de Paiements, Allocation Partielle de Paiements, Distribution de Paiements, '
               'Réconciliation de Paiements, Distribution Partielle de Paiements, Allocation de Ventes, '
               'Allocation d\'Achats',
    'version': '********.0',
    'author': 'Openinside',
    'website': 'https://www.open-inside.com',
    'category': 'Comptabilité',
    'description': '''
L'Allocation Avancée de Paiements est un outil essentiel et indispensable pour toute entreprise.
Odoo ne dispose pas d'une fonctionnalité pour allouer les paiements des factures clients/fournisseurs.
Ce module permet de faire des paiements anticipés à partir des factures clients/fournisseurs et de générer
les écritures comptables associées. Cette fonctionnalité permet de réviser toutes les factures du
client/fournisseur sélectionné et d'effectuer l'allocation du montant de paiement pour chaque facture.

Fonctionnalités modernisées pour Odoo 17.0 :
- Interface utilisateur repensée avec composants modernes
- Optimisations de performance
- Code refactorisé selon les bonnes pratiques
- Tests unitaires complets
- Support JavaScript/OWL pour l'interactivité
    ''',
    'depends': ['account'],
    'data': [
        'security/ir.model.access.csv',
        'views/account_payment_allocation.xml',
        'views/account_partial_reconcile.xml',
        'views/account_full_reconcile.xml',
        'views/account_payment.xml',
        'views/action.xml',
        'views/menu.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'oi_payment_allocation/static/src/js/**/*',
            'oi_payment_allocation/static/src/css/**/*',
        ],
    },
    'demo': [],
    'installable': True,
    'auto_install': False,
    'license': 'OPL-1',
    'price': 139.0,
    'currency': 'USD',
    'odoo-apps': True,
    'images': ['static/description/cover.png'],
    'application': False,
    'development_status': 'Production/Stable',
    'maintainers': ['openinside'],
}