"""
Module d'allocation de paiement optimisé pour Odoo 17.0
Améliore les performances avec mise en cache et requêtes optimisées
"""

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.fields import Command
from odoo.osv import expression
from odoo.tools import float_compare, float_is_zero
from collections import defaultdict
import logging

_logger = logging.getLogger(__name__)


class PaymentAllocationOptimized(models.TransientModel):
    """
    Version optimisée du modèle d'allocation de paiement
    """
    _name = "account.payment.allocation"
    _description = 'Allocation de Paiement'
    _check_company_auto = True

    # ========== CHAMPS ==========
    
    # Champs de base
    partner_id = fields.Many2one(
        'res.partner', 
        string="Partenaire",
        help="Partenaire pour filtrer les écritures"
    )
    account_id = fields.Many2one(
        'account.account', 
        string="Compte", 
        required=True,
        domain="[('company_id', '=', company_id), ('reconcile', '=', True)]",
        help="Compte de réconciliation"
    )
    show_child = fields.Boolean(
        string='Afficher parent/enfants',
        help="Afficher les partenaires enfants dans les lignes"
    )
    
    # Relations
    line_ids = fields.One2many(
        'account.payment.allocation.line', 
        'allocation_id',
        string="Lignes d'allocation"
    )
    debit_line_ids = fields.One2many(
        'account.payment.allocation.line', 
        'allocation_id', 
        domain=[('type', '=', 'debit')],
        string="Lignes de débit"
    )
    credit_line_ids = fields.One2many(
        'account.payment.allocation.line', 
        'allocation_id', 
        domain=[('type', '=', 'credit')],
        string="Lignes de crédit"
    )
    
    # Société et devise
    company_id = fields.Many2one(
        'res.company', 
        string="Société",
        required=True, 
        default=lambda self: self.env.company.id
    )
    currency_id = fields.Many2one(
        'res.currency', 
        string="Devise",
        required=True, 
        default=lambda self: self.env.company.currency_id.id
    )
    
    # Calculs
    balance = fields.Monetary(
        string="Solde",
        compute='_compute_balance',
        store=False,
        help="Différence entre débits et crédits alloués"
    )
    
    # Contexte
    payment_ids = fields.Many2many(
        'account.payment', 
        string="Paiements",
        default=lambda self: self._get_default_payments()
    )
    invoice_ids = fields.Many2many(
        'account.move', 
        string="Factures",
        default=lambda self: self._get_default_invoices()
    )
    move_line_ids = fields.Many2many(
        'account.move.line', 
        string="Écritures comptables",
        default=lambda self: self._get_default_move_lines()
    )
    
    # Écarts d'arrondi
    writeoff_journal_id = fields.Many2one(
        'account.journal', 
        string='Journal d\'écart'
    )
    writeoff_ref = fields.Char(
        string='Référence d\'écart'
    )
    writeoff_line_ids = fields.One2many(
        'account.payment.allocation.writeoff',
        'allocation_id',
        string="Lignes d'écart"
    )
    
    # Écriture compte/partenaire
    create_entry = fields.Boolean(
        string='Créer écriture compte/partenaire'
    )
    entry_journal_id = fields.Many2one(
        'account.journal', 
        string='Journal écriture compte/partenaire'
    )
    entry_name = fields.Char(
        string='Référence écriture'
    )
    
    # Filtres
    date_from = fields.Date(string="Date de début")
    date_to = fields.Date(string="Date de fin")
    ref = fields.Char(string='Référence')
    
    # Champs calculés
    max_date = fields.Date(
        string="Date maximum",
        compute='_compute_max_date',
        store=False
    )
    
    # ========== MÉTHODES PAR DÉFAUT ==========
    
    @api.model
    def _get_default_payments(self):
        """Récupère les paiements par défaut depuis le contexte"""
        if self._context.get('active_model') == 'account.payment':
            return [(6, 0, self._context.get('active_ids', []))]
        return []
    
    @api.model
    def _get_default_invoices(self):
        """Récupère les factures par défaut depuis le contexte"""
        if self._context.get('active_model') == 'account.move':
            return [(6, 0, self._context.get('active_ids', []))]
        return []
    
    @api.model
    def _get_default_move_lines(self):
        """Récupère les écritures par défaut depuis le contexte"""
        if self._context.get('active_model') == 'account.move.line':
            return [(6, 0, self._context.get('active_ids', []))]
        return []
    
    # ========== MÉTHODES CALCULÉES ==========
    
    @api.depends('debit_line_ids.allocate_amount', 'credit_line_ids.allocate_amount', 'writeoff_line_ids.balance')
    def _compute_balance(self):
        """Calcule le solde de l'allocation de manière optimisée"""
        for record in self:
            balance = 0.0
            
            # Utilise read_group pour optimiser les calculs
            if record.line_ids:
                # Calcul optimisé avec une seule requête
                allocated_lines = record.line_ids.filtered('allocate')
                for line in allocated_lines:
                    balance += line.allocate_amount * line.sign
            
            # Ajoute les écarts d'arrondi
            balance += sum(record.writeoff_line_ids.mapped('balance'))
            
            record.balance = balance
    
    @api.depends('line_ids.move_line_id.date')
    def _compute_max_date(self):
        """Calcule la date maximum des écritures allouées"""
        for record in self:
            allocated_lines = record.line_ids.filtered('allocate')
            if allocated_lines:
                dates = allocated_lines.mapped('move_line_id.date')
                record.max_date = max(dates) if dates else fields.Date.today()
            else:
                record.max_date = fields.Date.today()
    
    # ========== VALIDATION ==========
    
    @api.model
    def default_get(self, fields_list):
        """Initialisation par défaut optimisée"""
        result = super().default_get(fields_list)
        
        # Vérification de compatibilité
        if (self._context.get("active_model") == 'account.move' and 
            self.env['ir.config_parameter'].sudo().get_param('payment.allocation.invoice_disabled') == 'True'):
            
            invoice = self.env['account.move'].browse(self._context.get('active_id'))
            if invoice.exists() and invoice.move_type not in ['out_refund', 'in_refund']:
                raise UserError(_("Veuillez utiliser l'allocation à partir du paiement / notes de crédit"))
        
        return result
    
    @api.constrains('line_ids')
    def _check_allocation_balance(self):
        """Vérifie la cohérence de l'allocation"""
        for record in self:
            if record.line_ids:
                allocated_debits = record.debit_line_ids.filtered('allocate')
                allocated_credits = record.credit_line_ids.filtered('allocate')
                
                if not allocated_debits and not allocated_credits:
                    continue
                    
                if not allocated_debits or not allocated_credits:
                    raise ValidationError(
                        _("Une allocation doit contenir au moins une ligne de débit et une ligne de crédit.")
                    )
    
    # ========== MÉTHODES MÉTIER ==========
    
    def refresh_lines(self):
        """Actualise les lignes d'allocation de manière optimisée"""
        self.ensure_one()
        
        # Supprime les lignes existantes
        self.line_ids.unlink()
        
        # Construit le domaine de recherche optimisé
        domain = self._build_move_lines_domain()
        
        # Recherche optimisée avec prefetch
        move_lines = self.env['account.move.line'].search(
            domain, 
            order='date_maturity, date, move_name, id'
        )
        
        # Précharge les données nécessaires
        move_lines._prefetch_ids = move_lines.ids
        move_lines.read(['debit', 'credit', 'amount_residual', 'amount_residual_currency'])
        
        # Crée les lignes par batch
        self._create_allocation_lines_batch(move_lines)
        
        return True
    
    def _build_move_lines_domain(self):
        """Construit le domaine de recherche optimisé"""
        domain = [
            ('account_id', '=', self.account_id.id),
            ('reconciled', '=', False),
            ('company_id', '=', self.company_id.id),
            ('move_id.state', '=', 'posted'),
        ]
        
        # Filtre par partenaire
        if self.partner_id:
            if self.show_child:
                partner_ids = self.partner_id.child_ids.ids + [self.partner_id.id]
                domain.append(('partner_id', 'in', partner_ids))
            else:
                domain.append(('partner_id', '=', self.partner_id.id))
        
        # Filtre par dates
        if self.date_from:
            domain.append(('date', '>=', self.date_from))
        if self.date_to:
            domain.append(('date', '<=', self.date_to))
        
        # Filtre par référence
        if self.ref:
            domain.append('|')
            domain.append(('move_id.ref', 'ilike', self.ref))
            domain.append(('ref', 'ilike', self.ref))
        
        # Inclut les écritures actives
        active_move_lines = (
            self.payment_ids.line_ids + 
            self.invoice_ids.line_ids + 
            self.move_line_ids
        ).filtered(lambda l: l.account_id == self.account_id)
        
        if active_move_lines:
            domain = expression.OR([
                domain,
                [('id', 'in', active_move_lines.ids)]
            ])
        
        return domain
    
    def _create_allocation_lines_batch(self, move_lines):
        """Crée les lignes d'allocation par batch pour optimiser les performances"""
        line_vals_list = []
        
        for move_line in move_lines:
            line_type = 'debit' if move_line.debit else 'credit'
            allocate = self._should_auto_allocate(move_line)
            
            vals = {
                'allocation_id': self.id,
                'move_line_id': move_line.id,
                'allocate': allocate,
                'type': line_type,
            }
            line_vals_list.append(vals)
        
        # Création par batch
        if line_vals_list:
            self.env['account.payment.allocation.line'].create(line_vals_list)
    
    def _should_auto_allocate(self, move_line):
        """Détermine si une ligne doit être automatiquement allouée"""
        return (
            move_line.payment_id in self.payment_ids or
            move_line.move_id in self.invoice_ids or
            move_line in self.move_line_ids
        )
    
    def auto_allocate_lines(self):
        """Allocation automatique intelligente"""
        self.ensure_one()
        
        try:
            debit_lines = self.debit_line_ids.filtered(lambda l: not float_is_zero(l.amount_residual_display, precision_digits=2))
            credit_lines = self.credit_line_ids.filtered(lambda l: not float_is_zero(l.amount_residual_display, precision_digits=2))
            
            if not debit_lines or not credit_lines:
                return {'success': False, 'message': _("Aucune ligne disponible pour l'allocation automatique")}
            
            # Algorithme d'allocation optimisé
            self._perform_smart_allocation(debit_lines, credit_lines)
            
            return {'success': True, 'message': _("Allocation automatique effectuée")}
            
        except Exception as e:
            _logger.error("Erreur lors de l'allocation automatique: %s", str(e))
            return {'success': False, 'message': str(e)}
    
    def _perform_smart_allocation(self, debit_lines, credit_lines):
        """Effectue l'allocation intelligente"""
        # Trie les lignes par montant décroissant
        debit_lines = debit_lines.sorted('amount_residual_display', reverse=True)
        credit_lines = credit_lines.sorted('amount_residual_display', reverse=True)
        
        # Réinitialise les allocations
        (debit_lines + credit_lines).write({'allocate': False, 'allocate_amount': 0})
        
        # Allocation par correspondance exacte d'abord
        self._allocate_exact_matches(debit_lines, credit_lines)
        
        # Allocation par montant optimal
        self._allocate_optimal_amounts(debit_lines, credit_lines)
    
    def _allocate_exact_matches(self, debit_lines, credit_lines):
        """Alloue les correspondances exactes"""
        for debit_line in debit_lines:
            if debit_line.allocate:
                continue
                
            for credit_line in credit_lines:
                if credit_line.allocate:
                    continue
                    
                if float_compare(
                    abs(debit_line.amount_residual_display),
                    abs(credit_line.amount_residual_display),
                    precision_digits=2
                ) == 0:
                    # Correspondance exacte trouvée
                    debit_line.write({
                        'allocate': True,
                        'allocate_amount': abs(debit_line.amount_residual_display)
                    })
                    credit_line.write({
                        'allocate': True,
                        'allocate_amount': abs(credit_line.amount_residual_display)
                    })
                    break
    
    def _allocate_optimal_amounts(self, debit_lines, credit_lines):
        """Alloue les montants de manière optimale"""
        available_debits = debit_lines.filtered(lambda l: not l.allocate)
        available_credits = credit_lines.filtered(lambda l: not l.allocate)
        
        for debit_line in available_debits:
            remaining_debit = abs(debit_line.amount_residual_display)
            
            for credit_line in available_credits:
                if credit_line.allocate:
                    continue
                    
                remaining_credit = abs(credit_line.amount_residual_display)
                allocate_amount = min(remaining_debit, remaining_credit)
                
                if allocate_amount > 0:
                    # Active l'allocation
                    if not debit_line.allocate:
                        debit_line.write({'allocate': True, 'allocate_amount': 0})
                    if not credit_line.allocate:
                        credit_line.write({'allocate': True, 'allocate_amount': 0})
                    
                    # Met à jour les montants
                    debit_line.allocate_amount += allocate_amount
                    credit_line.allocate_amount += allocate_amount
                    
                    remaining_debit -= allocate_amount
                    
                    if float_is_zero(remaining_debit, precision_digits=2):
                        break
    
    def validate(self):
        """Validation optimisée de l'allocation"""
        self.ensure_one()
        
        # Vérifications préliminaires
        self._validate_allocation_requirements()
        
        # Traitement des écarts d'arrondi
        if self.writeoff_journal_id and self.writeoff_line_ids:
            self._process_writeoff_entries()
        
        # Création des réconciliations
        self._create_reconciliations()
        
        # Création des écritures compte/partenaire si nécessaire
        if self.create_entry:
            self._create_partner_entries()
        
        return {'type': 'ir.actions.act_window_close'}
    
    def _validate_allocation_requirements(self):
        """Valide les prérequis de l'allocation"""
        allocated_debits = self.debit_line_ids.filtered(lambda l: l.allocate and l.allocate_amount)
        allocated_credits = self.credit_line_ids.filtered(lambda l: l.allocate and l.allocate_amount)
        
        if not allocated_debits or not allocated_credits:
            raise UserError(_('Sélectionnez au moins une ligne de débit et une ligne de crédit'))
        
        # Vérification des écritures
        move_lines = (allocated_debits + allocated_credits).mapped('move_line_id')
        self._check_move_lines_validity(move_lines)
    
    def _check_move_lines_validity(self, move_lines):
        """Vérifie la validité des écritures comptables"""
        for line in move_lines:
            if line.reconciled:
                raise UserError(_("Vous essayez de réconcilier des écritures déjà réconciliées."))
            
            if not line.account_id.reconcile and line.account_id.account_type not in ('asset_cash', 'liability_credit_card'):
                raise UserError(
                    _("Le compte %s n'autorise pas la réconciliation. "
                      "Modifiez d'abord la configuration de ce compte.") % line.account_id.display_name
                )
            
            if line.move_id.state != 'posted':
                raise UserError(_('Vous ne pouvez réconcilier que des écritures validées.'))

    def _process_writeoff_entries(self):
        """Traite les écritures d'écart d'arrondi de manière optimisée"""
        if not self.writeoff_journal_id or not self.writeoff_line_ids:
            return

        max_date = self.max_date

        # Calcul du montant total d'écart
        writeoff_total = sum(self.writeoff_line_ids.mapped('balance'))

        # Préparation des lignes d'écriture
        move_lines = []
        for line in self.writeoff_line_ids:
            move_lines.append(Command.create({
                'account_id': line.account_id.id,
                'currency_id': line.currency_id.id,
                'amount_currency': -line.balance,
                'partner_id': line.partner_id.id,
                'product_id': line.product_id.id,
                'name': line.name,
                'tax_ids': [Command.set(line.tax_ids.ids)] if line.tax_ids else [],
                'analytic_distribution': line.analytic_distribution,
                'display_type': 'product'
            }))

        # Ligne de contrepartie
        move_lines.append(Command.create({
            'account_id': self.account_id.id,
            'currency_id': self.currency_id.id,
            'amount_currency': writeoff_total,
            'partner_id': self.partner_id.id,
            'display_type': 'payment_term' if self.account_id.account_type in ['asset_receivable', 'liability_payable'] else 'product'
        }))

        # Création de l'écriture
        move_vals = {
            'journal_id': self.writeoff_journal_id.id,
            'ref': self.writeoff_ref or _('Écart d\'arrondi - Allocation'),
            'date': max_date,
            'line_ids': move_lines,
            'move_type': 'entry',
            'partner_id': self.partner_id.id
        }

        move = self.env['account.move'].with_context(skip_invoice_sync=True).create(move_vals)
        move._post()

        # Ajoute la ligne d'écart aux lignes d'allocation
        writeoff_move_line = move.line_ids.filtered(lambda l: l.account_id == self.account_id)
        if writeoff_move_line:
            self.env["account.payment.allocation.line"].create({
                'allocation_id': self.id,
                'type': 'debit' if writeoff_move_line.debit else 'credit',
                'move_line_id': writeoff_move_line.id,
                'allocate': True,
                'allocate_amount': abs(writeoff_move_line.amount_currency or writeoff_move_line.balance),
            })

    def _create_reconciliations(self):
        """Crée les réconciliations de manière optimisée"""
        allocated_debits = self.debit_line_ids.filtered(lambda l: l.allocate and l.allocate_amount)
        allocated_credits = self.credit_line_ids.filtered(lambda l: l.allocate and l.allocate_amount)

        if not allocated_debits or not allocated_credits:
            return

        max_date = self.max_date
        partial_reconciles = self.env["account.partial.reconcile"]

        # Optimisation: traitement par batch
        reconcile_data = []

        for debit_line in allocated_debits:
            remaining_debit = debit_line.allocate_amount

            for credit_line in allocated_credits:
                if float_is_zero(remaining_debit, precision_digits=2):
                    break

                remaining_credit = credit_line.allocate_amount
                if float_is_zero(remaining_credit, precision_digits=2):
                    continue

                allocate_amount = min(remaining_debit, remaining_credit)

                # Conversion de devise
                company_amount = self.currency_id._convert(
                    allocate_amount,
                    self.company_id.currency_id,
                    self.company_id,
                    max_date
                )

                debit_amount_currency = self.currency_id._convert(
                    allocate_amount,
                    debit_line.move_line_id.currency_id or self.company_id.currency_id,
                    self.company_id,
                    max_date
                )

                credit_amount_currency = self.currency_id._convert(
                    allocate_amount,
                    credit_line.move_line_id.currency_id or self.company_id.currency_id,
                    self.company_id,
                    max_date
                )

                reconcile_data.append({
                    'debit_move_id': debit_line.move_line_id.id,
                    'credit_move_id': credit_line.move_line_id.id,
                    'amount': company_amount,
                    'debit_amount_currency': debit_amount_currency,
                    'credit_amount_currency': credit_amount_currency,
                })

                # Met à jour les montants restants
                remaining_debit -= allocate_amount
                credit_line.allocate_amount -= allocate_amount

        # Création par batch des réconciliations partielles
        if reconcile_data:
            partial_reconciles = self.env["account.partial.reconcile"].create(reconcile_data)

        # Création de la réconciliation complète si nécessaire
        self._create_full_reconciliation(partial_reconciles)

    def _create_full_reconciliation(self, partial_reconciles):
        """Crée la réconciliation complète si toutes les lignes sont réconciliées"""
        if not partial_reconciles:
            return

        # Récupère toutes les lignes réconciliées
        reconciled_lines = self.env['account.move.line']
        for reconcile in partial_reconciles:
            reconciled_lines |= reconcile.debit_move_id | reconcile.credit_move_id

        # Vérifie si toutes les lignes sont complètement réconciliées
        fully_reconciled = reconciled_lines.filtered('reconciled')

        if fully_reconciled:
            # Crée la réconciliation complète
            self.env["account.full.reconcile"].create({
                'partial_reconcile_ids': [(6, 0, partial_reconciles.ids)],
                'reconciled_line_ids': [(6, 0, fully_reconciled.ids)],
            })

    def _create_partner_entries(self):
        """Crée les écritures compte/partenaire si nécessaire"""
        if not self.create_entry or not self.entry_journal_id:
            return

        # Calcule les soldes par partenaire
        partner_balances = self._calculate_partner_balances()

        if not partner_balances:
            return

        # Crée l'écriture de régularisation
        move_lines = []
        for partner_id, balance in partner_balances.items():
            if not float_is_zero(balance, precision_digits=2):
                move_lines.append(Command.create({
                    'account_id': self.account_id.id,
                    'name': self.entry_name or _('Régularisation partenaire'),
                    'partner_id': partner_id,
                    'credit': balance > 0 and balance or 0,
                    'debit': balance < 0 and -balance or 0,
                    'currency_id': self.currency_id.id if self.currency_id != self.company_id.currency_id else False,
                    'amount_currency': balance if self.currency_id != self.company_id.currency_id else 0,
                }))

        if move_lines:
            move_vals = {
                'journal_id': self.entry_journal_id.id,
                'ref': self.entry_name or _('Régularisation - Allocation de paiement'),
                'date': self.max_date,
                'line_ids': move_lines,
                'move_type': 'entry',
            }

            move = self.env['account.move'].with_context(skip_invoice_sync=True).create(move_vals)
            move._post()

            # Réconcilie automatiquement les lignes créées
            move.line_ids.reconcile()

    def _calculate_partner_balances(self):
        """Calcule les soldes par partenaire pour les écritures de régularisation"""
        partner_balances = defaultdict(float)

        allocated_lines = self.line_ids.filtered('allocate')
        partner_ids = allocated_lines.mapped('move_line_id.partner_id')

        if len(partner_ids) <= 1:
            return {}

        for line in allocated_lines:
            partner_id = line.move_line_id.partner_id.id
            amount = line.allocate_amount * line.sign
            partner_balances[partner_id] += amount

        return dict(partner_balances)

    def reset_allocations(self):
        """Réinitialise toutes les allocations"""
        self.ensure_one()

        # Désélectionne toutes les lignes
        self.line_ids.write({
            'allocate': False,
            'allocate_amount': 0.0,
        })

        # Recalcule le solde
        self._compute_balance()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _("Réinitialisation"),
                'message': _("Toutes les allocations ont été réinitialisées"),
                'type': 'info',
                'sticky': False,
            }
        }

    def allocate_oldest_first(self):
        """Alloue les lignes les plus anciennes en premier"""
        self.ensure_one()

        # Trie les lignes par date d'échéance puis par date
        debit_lines = self.debit_line_ids.sorted(lambda l: (l.date_maturity or l.date, l.date))
        credit_lines = self.credit_line_ids.sorted(lambda l: (l.date_maturity or l.date, l.date))

        allocated_count = 0

        # Alloue les débits aux crédits dans l'ordre chronologique
        for debit_line in debit_lines:
            if float_is_zero(debit_line.amount_residual_display, precision_digits=2):
                continue

            remaining_debit = abs(debit_line.amount_residual_display)

            for credit_line in credit_lines:
                if float_is_zero(credit_line.amount_residual_display, precision_digits=2):
                    continue

                available_credit = abs(credit_line.amount_residual_display)
                allocation_amount = min(remaining_debit, available_credit)

                if allocation_amount > 0:
                    debit_line.write({
                        'allocate': True,
                        'allocate_amount': allocation_amount,
                    })
                    credit_line.write({
                        'allocate': True,
                        'allocate_amount': allocation_amount,
                    })

                    remaining_debit -= allocation_amount
                    allocated_count += 2

                    if float_is_zero(remaining_debit, precision_digits=2):
                        break

        self._compute_balance()

        return {
            'success': True,
            'allocated_count': allocated_count,
            'message': _("Allocation par ancienneté effectuée")
        }

    def allocate_exact_matches(self):
        """Alloue les correspondances exactes de montant"""
        self.ensure_one()

        matched_count = 0

        for debit_line in self.debit_line_ids:
            if debit_line.allocate:
                continue

            debit_amount = abs(debit_line.amount_residual_display)

            # Cherche un crédit de montant exact
            for credit_line in self.credit_line_ids:
                if credit_line.allocate:
                    continue

                credit_amount = abs(credit_line.amount_residual_display)

                if float_compare(debit_amount, credit_amount, precision_digits=2) == 0:
                    # Correspondance exacte trouvée
                    debit_line.write({
                        'allocate': True,
                        'allocate_amount': debit_amount,
                    })
                    credit_line.write({
                        'allocate': True,
                        'allocate_amount': credit_amount,
                    })

                    matched_count += 2
                    break

        self._compute_balance()

        return {
            'success': True,
            'matched_count': matched_count,
            'message': _("Correspondances exactes allouées")
        }

    def allocate_by_reference(self):
        """Alloue par correspondance de référence"""
        self.ensure_one()

        matched_count = 0

        for debit_line in self.debit_line_ids:
            if debit_line.allocate or not debit_line.ref:
                continue

            # Cherche un crédit avec la même référence
            for credit_line in self.credit_line_ids:
                if credit_line.allocate or not credit_line.ref:
                    continue

                if debit_line.ref == credit_line.ref:
                    # Correspondance de référence trouvée
                    debit_amount = abs(debit_line.amount_residual_display)
                    credit_amount = abs(credit_line.amount_residual_display)
                    allocation_amount = min(debit_amount, credit_amount)

                    debit_line.write({
                        'allocate': True,
                        'allocate_amount': allocation_amount,
                    })
                    credit_line.write({
                        'allocate': True,
                        'allocate_amount': allocation_amount,
                    })

                    matched_count += 2
                    break

        self._compute_balance()

        return {
            'success': True,
            'matched_count': matched_count,
            'message': _("Allocations par référence effectuées")
        }

    def get_allocation_preview(self):
        """Retourne un aperçu de l'allocation actuelle"""
        self.ensure_one()

        allocated_lines = self.line_ids.filtered('allocate')
        debit_lines = allocated_lines.filtered(lambda l: l.type == 'debit')
        credit_lines = allocated_lines.filtered(lambda l: l.type == 'credit')

        return {
            'total_debit': sum(debit_lines.mapped('allocate_amount')),
            'total_credit': sum(credit_lines.mapped('allocate_amount')),
            'balance': self.balance,
            'allocated_lines': len(allocated_lines),
            'debit_count': len(debit_lines),
            'credit_count': len(credit_lines),
            'is_balanced': float_is_zero(self.balance, precision_digits=2),
            'currency_symbol': self.currency_id.symbol or self.currency_id.name,
        }
