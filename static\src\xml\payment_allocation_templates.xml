<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Template principal du widget d'allocation -->
    <t t-name="oi_payment_allocation.PaymentAllocationWidget" owl="1">
        <div class="o_payment_allocation_widget">
            
            <!-- Barr<PERSON> d'outils -->
            <div class="o_payment_allocation_toolbar mb-3">
                <div class="btn-group" role="group">
                    <button type="button" 
                            class="btn btn-primary btn-sm"
                            t-on-click="autoAllocate"
                            t-att-disabled="state.loading or props.readonly"
                            data-tooltip="Allocation automatique intelligente">
                        <i class="fa fa-magic" t-if="!state.loading"/>
                        <i class="fa fa-spinner fa-spin" t-if="state.loading"/>
                        Auto-Allocation
                    </button>
                    
                    <button type="button" 
                            class="btn btn-secondary btn-sm"
                            t-on-click="resetAllocations"
                            t-att-disabled="state.loading or props.readonly"
                            data-tooltip="Réinitialiser toutes les allocations">
                        <i class="fa fa-refresh"/>
                        Réinitialiser
                    </button>
                    
                    <button type="button" 
                            class="btn btn-outline-secondary btn-sm"
                            t-on-click="toggleAdvanced"
                            data-tooltip="Afficher/masquer les options avancées">
                        <i class="fa fa-cog"/>
                        <i class="fa fa-chevron-down" t-if="!state.showAdvanced"/>
                        <i class="fa fa-chevron-up" t-if="state.showAdvanced"/>
                    </button>
                </div>
            </div>

            <!-- Indicateurs de statut -->
            <div class="o_payment_allocation_status mb-3">
                <div class="alert" t-att-class="getStatusClass()" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <i class="fa fa-info-circle" t-if="!state.isBalanced"/>
                            <i class="fa fa-check-circle" t-if="state.isBalanced"/>
                            <span t-esc="getStatusMessage()"/>
                        </div>
                        <div class="ml-3">
                            <button type="button" 
                                    class="btn btn-success btn-sm"
                                    t-on-click="validateAllocation"
                                    t-att-disabled="state.loading or props.readonly"
                                    t-if="state.isBalanced">
                                <i class="fa fa-check"/>
                                Valider
                            </button>
                            <button type="button" 
                                    class="btn btn-warning btn-sm"
                                    t-on-click="validateAllocation"
                                    t-att-disabled="state.loading or props.readonly"
                                    t-if="!state.isBalanced">
                                <i class="fa fa-exclamation-triangle"/>
                                Valider avec écart
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Barre de progression -->
            <div class="o_payment_allocation_progress mb-3" t-if="state.debitTotal > 0 or state.creditTotal > 0">
                <div class="progress">
                    <div class="progress-bar" 
                         role="progressbar" 
                         aria-valuenow="0" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
                <small class="text-muted">
                    Progression de l'allocation : 
                    <span t-esc="formatCurrency(Math.min(state.debitTotal, state.creditTotal))"/> / 
                    <span t-esc="formatCurrency(Math.max(state.debitTotal, state.creditTotal))"/>
                </small>
            </div>

            <!-- Résumé des totaux -->
            <div class="o_payment_allocation_summary mb-3">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">
                                    <i class="fa fa-plus-circle"/>
                                    Débits
                                </h5>
                                <h3 class="card-text text-primary" t-esc="formatCurrency(state.debitTotal)"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">
                                    <i class="fa fa-minus-circle"/>
                                    Crédits
                                </h5>
                                <h3 class="card-text text-success" t-esc="formatCurrency(state.creditTotal)"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card" t-att-class="state.isBalanced ? 'border-success' : 'border-warning'">
                            <div class="card-body text-center">
                                <h5 class="card-title" t-att-class="state.isBalanced ? 'text-success' : 'text-warning'">
                                    <i class="fa fa-balance-scale"/>
                                    Solde
                                </h5>
                                <h3 class="card-text" 
                                    t-att-class="state.isBalanced ? 'text-success' : 'text-warning'"
                                    t-esc="formatCurrency(state.balance)"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Options avancées -->
            <div class="o_payment_allocation_advanced collapse" t-att-class="state.showAdvanced ? 'show' : ''">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa fa-cogs"/>
                            Options Avancées
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="autoAllocateEnabled"
                                           t-model="state.autoAllocateEnabled"/>
                                    <label class="form-check-label" for="autoAllocateEnabled">
                                        Allocation automatique activée
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="toleranceAmount">Tolérance d'écart</label>
                                    <input type="number" 
                                           class="form-control form-control-sm" 
                                           id="toleranceAmount"
                                           step="0.01"
                                           placeholder="0.01"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Raccourcis clavier -->
            <div class="o_payment_allocation_shortcuts mt-3" t-if="state.showAdvanced">
                <small class="text-muted">
                    <strong>Raccourcis :</strong>
                    <kbd>Ctrl+A</kbd> Auto-allocation |
                    <kbd>Ctrl+R</kbd> Réinitialiser |
                    <kbd>Ctrl+Enter</kbd> Valider |
                    <kbd>Ctrl+?</kbd> Aide
                </small>
            </div>
        </div>
    </t>

    <!-- Template pour les lignes d'allocation améliorées -->
    <t t-name="oi_payment_allocation.AllocationLine" owl="1">
        <tr class="o_allocation_line" 
            t-att-class="props.line.allocate ? 'table-success' : 'table-light'">
            
            <!-- Checkbox d'allocation -->
            <td class="text-center">
                <div class="form-check">
                    <input class="form-check-input" 
                           type="checkbox" 
                           t-model="props.line.allocate"
                           t-on-change="onAllocateChange"/>
                </div>
            </td>

            <!-- Document -->
            <td>
                <div class="d-flex align-items-center">
                    <i class="fa fa-file-text-o mr-2 text-muted"/>
                    <span t-esc="props.line.document_name"/>
                    <span class="badge badge-info ml-2" t-if="props.line.refund">
                        Avoir
                    </span>
                </div>
            </td>

            <!-- Montant résiduel -->
            <td class="text-right">
                <span class="font-weight-bold" 
                      t-att-class="props.line.amount_residual_display > 0 ? 'text-success' : 'text-danger'"
                      t-esc="formatCurrency(props.line.amount_residual_display)"/>
            </td>

            <!-- Montant alloué -->
            <td class="text-right">
                <input type="number" 
                       class="form-control form-control-sm text-right"
                       t-model="props.line.allocate_amount"
                       t-att-readonly="!props.line.allocate"
                       t-on-change="onAmountChange"
                       step="0.01"/>
            </td>

            <!-- Actions -->
            <td class="text-center">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" 
                            class="btn btn-outline-primary btn-sm"
                            t-on-click="allocateMax"
                            t-att-disabled="!props.line.allocate"
                            data-tooltip="Allouer le montant maximum">
                        <i class="fa fa-arrow-up"/>
                    </button>
                    <button type="button" 
                            class="btn btn-outline-secondary btn-sm"
                            t-on-click="clearAllocation"
                            data-tooltip="Effacer l'allocation">
                        <i class="fa fa-times"/>
                    </button>
                </div>
            </td>
        </tr>
    </t>

    <!-- Template pour les indicateurs de devise -->
    <t t-name="oi_payment_allocation.CurrencyIndicator" owl="1">
        <span class="o_currency_indicator badge" 
              t-att-class="props.isCompanyCurrency ? 'badge-primary' : 'badge-secondary'">
            <i class="fa fa-money mr-1"/>
            <span t-esc="props.currencyName"/>
            <span t-if="props.rate and props.rate !== 1.0" class="ml-1">
                (1:<span t-esc="props.rate.toFixed(4)"/>)
            </span>
        </span>
    </t>

    <!-- Template pour les alertes contextuelles -->
    <t t-name="oi_payment_allocation.ContextAlert" owl="1">
        <div class="alert alert-dismissible fade show" 
             t-att-class="'alert-' + props.type" 
             role="alert">
            <i class="fa" t-att-class="getAlertIcon()"/>
            <strong t-if="props.title" t-esc="props.title"/>
            <span t-esc="props.message"/>
            <button type="button" 
                    class="close" 
                    t-on-click="dismiss"
                    aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </t>

</templates>
