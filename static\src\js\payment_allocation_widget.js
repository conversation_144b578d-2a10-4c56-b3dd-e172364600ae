/** @odoo-module **/

import { Component, useState, onWillStart, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";

/**
 * Widget moderne pour l'allocation de paiement
 * Améliore l'expérience utilisateur avec des interactions en temps réel
 */
export class PaymentAllocationWidget extends Component {
    static template = "oi_payment_allocation.PaymentAllocationWidget";
    static props = {
        record: Object,
        readonly: { type: Boolean, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        
        this.state = useState({
            debitTotal: 0,
            creditTotal: 0,
            balance: 0,
            isBalanced: false,
            loading: false,
            autoAllocateEnabled: true,
            showAdvanced: false,
        });

        onWillStart(this.onWillStart);
        onMounted(this.onMounted);
    }

    async onWillStart() {
        await this.updateTotals();
    }

    onMounted() {
        this.setupEventListeners();
        this.initializeTooltips();
    }

    /**
     * Met à jour les totaux en temps réel
     */
    async updateTotals() {
        const record = this.props.record;
        let debitTotal = 0;
        let creditTotal = 0;

        // Calcul des totaux débits
        if (record.data.debit_line_ids) {
            for (const lineId of record.data.debit_line_ids.records) {
                const line = lineId.data;
                if (line.allocate && line.allocate_amount) {
                    debitTotal += line.allocate_amount;
                }
            }
        }

        // Calcul des totaux crédits
        if (record.data.credit_line_ids) {
            for (const lineId of record.data.credit_line_ids.records) {
                const line = lineId.data;
                if (line.allocate && line.allocate_amount) {
                    creditTotal += Math.abs(line.allocate_amount);
                }
            }
        }

        // Ajout des écarts d'arrondi
        let writeoffTotal = 0;
        if (record.data.writeoff_line_ids) {
            for (const lineId of record.data.writeoff_line_ids.records) {
                writeoffTotal += lineId.data.balance || 0;
            }
        }

        const balance = debitTotal - creditTotal + writeoffTotal;

        this.state.debitTotal = debitTotal;
        this.state.creditTotal = creditTotal;
        this.state.balance = balance;
        this.state.isBalanced = Math.abs(balance) < 0.01;
    }

    /**
     * Configuration des écouteurs d'événements
     */
    setupEventListeners() {
        // Écoute les changements sur les lignes d'allocation
        const form = this.el.closest('.o_form_view');
        if (form) {
            form.addEventListener('change', this.onFormChange.bind(this));
        }
    }

    /**
     * Gestionnaire de changement de formulaire
     */
    async onFormChange(event) {
        if (event.target.matches('[name*="allocate"], [name*="allocate_amount"]')) {
            await this.updateTotals();
            this.updateProgressBar();
        }
    }

    /**
     * Met à jour la barre de progression
     */
    updateProgressBar() {
        const progressBar = this.el.querySelector('.o_payment_allocation_progress .progress-bar');
        if (progressBar) {
            const total = Math.max(this.state.debitTotal, this.state.creditTotal);
            const allocated = Math.min(this.state.debitTotal, this.state.creditTotal);
            const percentage = total > 0 ? (allocated / total) * 100 : 0;
            
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }
    }

    /**
     * Allocation automatique intelligente
     */
    async autoAllocate() {
        if (!this.state.autoAllocateEnabled) return;

        this.state.loading = true;
        try {
            const record = this.props.record;
            const result = await this.orm.call(
                'account.payment.allocation',
                'auto_allocate_lines',
                [record.resId],
                {
                    context: record.context,
                }
            );

            if (result.success) {
                await record.load();
                await this.updateTotals();
                this.notification.add(_t("Allocation automatique effectuée"), {
                    type: "success",
                });
            } else {
                this.notification.add(result.message || _t("Erreur lors de l'allocation automatique"), {
                    type: "warning",
                });
            }
        } catch (error) {
            this.notification.add(_t("Erreur lors de l'allocation automatique"), {
                type: "danger",
            });
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Réinitialise toutes les allocations
     */
    async resetAllocations() {
        const record = this.props.record;
        
        // Réinitialise les lignes de débit
        if (record.data.debit_line_ids) {
            for (const lineId of record.data.debit_line_ids.records) {
                await lineId.update({
                    allocate: false,
                    allocate_amount: 0,
                });
            }
        }

        // Réinitialise les lignes de crédit
        if (record.data.credit_line_ids) {
            for (const lineId of record.data.credit_line_ids.records) {
                await lineId.update({
                    allocate: false,
                    allocate_amount: 0,
                });
            }
        }

        await this.updateTotals();
        this.notification.add(_t("Allocations réinitialisées"), {
            type: "info",
        });
    }

    /**
     * Bascule l'affichage des options avancées
     */
    toggleAdvanced() {
        this.state.showAdvanced = !this.state.showAdvanced;
    }

    /**
     * Initialise les tooltips
     */
    initializeTooltips() {
        const tooltipElements = this.el.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    /**
     * Affiche un tooltip
     */
    showTooltip(event) {
        const element = event.target;
        const tooltipText = element.getAttribute('data-tooltip');
        
        if (!tooltipText) return;

        const tooltip = document.createElement('div');
        tooltip.className = 'o_payment_allocation_tooltip';
        tooltip.textContent = tooltipText;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
        
        element._tooltip = tooltip;
    }

    /**
     * Cache un tooltip
     */
    hideTooltip(event) {
        const element = event.target;
        if (element._tooltip) {
            document.body.removeChild(element._tooltip);
            delete element._tooltip;
        }
    }

    /**
     * Valide l'allocation avec vérifications
     */
    async validateAllocation() {
        if (!this.state.isBalanced) {
            const confirmed = await this.dialog.add(ConfirmationDialog, {
                title: _t("Allocation non équilibrée"),
                body: _t("L'allocation n'est pas équilibrée. Voulez-vous continuer avec un écart de %s ?", 
                        this.formatCurrency(this.state.balance)),
                confirm: () => this.performValidation(),
                cancel: () => {},
            });
            return;
        }

        await this.performValidation();
    }

    /**
     * Effectue la validation
     */
    async performValidation() {
        this.state.loading = true;
        try {
            const record = this.props.record;
            await this.orm.call(
                'account.payment.allocation',
                'validate',
                [record.resId],
                {
                    context: record.context,
                }
            );

            this.notification.add(_t("Allocation validée avec succès"), {
                type: "success",
            });

            // Ferme le dialogue
            if (record.model.env.services.action) {
                record.model.env.services.action.doAction({
                    type: 'ir.actions.act_window_close'
                });
            }
        } catch (error) {
            this.notification.add(_t("Erreur lors de la validation"), {
                type: "danger",
            });
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Formate un montant monétaire
     */
    formatCurrency(amount) {
        const record = this.props.record;
        const currency = record.data.currency_id;
        
        if (currency && currency.data) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: currency.data.name || 'EUR',
            }).format(amount);
        }
        
        return amount.toFixed(2);
    }

    /**
     * Obtient la classe CSS pour le statut
     */
    getStatusClass() {
        if (this.state.isBalanced) {
            return 'alert-success';
        } else if (Math.abs(this.state.balance) < 1) {
            return 'alert-warning';
        } else {
            return 'alert-info';
        }
    }

    /**
     * Obtient le message de statut
     */
    getStatusMessage() {
        if (this.state.isBalanced) {
            return _t("Allocation équilibrée - Prêt à valider");
        } else {
            return _t("Solde à équilibrer : %s", this.formatCurrency(this.state.balance));
        }
    }

    /**
     * Réinitialise toutes les allocations
     */
    async resetAllocations() {
        try {
            const resetBtn = document.querySelector('.o_reset_btn');
            if (resetBtn) {
                resetBtn.disabled = true;
                resetBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i><span class="btn-text">Réinitialisation...</span>';
            }

            const record = this.props.record;
            await this.orm.call(
                'account.payment.allocation',
                'reset_allocations',
                [record.resId],
                { context: record.context }
            );

            await this.updateTotals();

            this.notification.add('🔄 Allocations réinitialisées', {
                type: 'info',
                title: 'Réinitialisation'
            });

        } catch (error) {
            console.error('Erreur lors de la réinitialisation:', error);
            this.notification.add('❌ Erreur lors de la réinitialisation', {
                type: 'danger'
            });
        } finally {
            const resetBtn = document.querySelector('.o_reset_btn');
            if (resetBtn) {
                resetBtn.disabled = false;
                resetBtn.innerHTML = '<i class="fa fa-undo me-2"></i><span class="btn-text">Réinitialiser</span><small class="btn-subtitle">Effacer les allocations</small>';
            }
        }
    }

    /**
     * Allocation intelligente automatique
     */
    async autoAllocateSmart() {
        try {
            const autoBtn = document.querySelector('.o_auto_allocate_btn');
            if (autoBtn) {
                autoBtn.disabled = true;
                autoBtn.innerHTML = '<i class="fa fa-magic fa-spin me-2"></i><span class="btn-text">Allocation...</span>';
                autoBtn.classList.add('shine');
            }

            const record = this.props.record;
            const result = await this.orm.call(
                'account.payment.allocation',
                'auto_allocate_lines',
                [record.resId],
                { context: record.context }
            );

            if (result && result.success) {
                await this.updateTotals();

                this.notification.add(`🎯 ${result.allocated_count || 0} lignes allouées automatiquement`, {
                    type: 'success',
                    title: 'Auto-allocation réussie'
                });

                // Animation de succès
                if (autoBtn) {
                    autoBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    setTimeout(() => {
                        autoBtn.style.background = '';
                    }, 2000);
                }
            } else {
                this.notification.add('⚠️ Aucune correspondance automatique trouvée', {
                    type: 'warning',
                    title: 'Auto-allocation'
                });
            }

        } catch (error) {
            console.error('Erreur lors de l\'auto-allocation:', error);
            this.notification.add('❌ Erreur lors de l\'auto-allocation', {
                type: 'danger'
            });
        } finally {
            const autoBtn = document.querySelector('.o_auto_allocate_btn');
            if (autoBtn) {
                autoBtn.disabled = false;
                autoBtn.innerHTML = '<i class="fa fa-magic me-2"></i><span class="btn-text">Auto-Allocation</span><small class="btn-subtitle">Correspondance intelligente</small>';
                autoBtn.classList.remove('shine');
            }
        }
    }

    /**
     * Alloue les plus anciennes en premier
     */
    async allocateOldestFirst() {
        try {
            const record = this.props.record;
            await this.orm.call(
                'account.payment.allocation',
                'allocate_oldest_first',
                [record.resId],
                { context: record.context }
            );

            await this.updateTotals();
            this.notification.add('📅 Allocation par ancienneté effectuée', {
                type: 'success'
            });
        } catch (error) {
            this.notification.add('❌ Erreur lors de l\'allocation par ancienneté', {
                type: 'danger'
            });
        }
    }

    /**
     * Alloue les correspondances exactes
     */
    async allocateExactMatches() {
        try {
            const record = this.props.record;
            const result = await this.orm.call(
                'account.payment.allocation',
                'allocate_exact_matches',
                [record.resId],
                { context: record.context }
            );

            await this.updateTotals();
            this.notification.add(`🎯 ${result.matched_count || 0} correspondances exactes trouvées`, {
                type: 'success'
            });
        } catch (error) {
            this.notification.add('❌ Erreur lors de la recherche de correspondances exactes', {
                type: 'danger'
            });
        }
    }

    /**
     * Alloue par référence
     */
    async allocateByReference() {
        try {
            const record = this.props.record;
            const result = await this.orm.call(
                'account.payment.allocation',
                'allocate_by_reference',
                [record.resId],
                { context: record.context }
            );

            await this.updateTotals();
            this.notification.add(`🔗 ${result.matched_count || 0} allocations par référence`, {
                type: 'success'
            });
        } catch (error) {
            this.notification.add('❌ Erreur lors de l\'allocation par référence', {
                type: 'danger'
            });
        }
    }

    /**
     * Affiche un aperçu de l'allocation
     */
    async showAllocationPreview() {
        try {
            const record = this.props.record;
            const preview = await this.orm.call(
                'account.payment.allocation',
                'get_allocation_preview',
                [record.resId],
                { context: record.context }
            );

            // Affiche une modal avec l'aperçu
            this.dialog.add(AlertDialog, {
                title: '👁️ Aperçu de l\'allocation',
                body: this.renderPreview(preview),
                confirmLabel: 'Fermer'
            });
        } catch (error) {
            this.notification.add('❌ Erreur lors de la génération de l\'aperçu', {
                type: 'danger'
            });
        }
    }

    /**
     * Rend l'aperçu de l'allocation
     */
    renderPreview(preview) {
        if (!preview) return 'Aucune donnée d\'aperçu disponible';

        let html = '<div class="allocation-preview">';
        html += `<p><strong>Total Débits:</strong> ${this.formatCurrency(preview.total_debit)}</p>`;
        html += `<p><strong>Total Crédits:</strong> ${this.formatCurrency(preview.total_credit)}</p>`;
        html += `<p><strong>Solde:</strong> ${this.formatCurrency(preview.balance)}</p>`;
        html += `<p><strong>Lignes allouées:</strong> ${preview.allocated_lines}</p>`;
        html += '</div>';

        return html;
    }
}

// Enregistrement du composant
registry.category("fields").add("payment_allocation_widget", PaymentAllocationWidget);
