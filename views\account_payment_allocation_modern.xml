<odoo>
    <!-- Vue principale modernisée avec composants Odoo 17 -->
    <record id="view_account_payment_allocation_form_modern" model="ir.ui.view">
        <field name="name">account.payment.allocation.form.modern</field>
        <field name="model">account.payment.allocation</field>
        <field name="arch" type="xml">
            <form string="Allocation de Paiement" class="o_payment_allocation_form">
                <header>
                    <button name="validate" string="Valider l'Allocation" type="object" 
                            class="btn-primary" data-hotkey="v"
                            invisible="balance != 0"/>
                    <button name="validate" string="Valider avec Écart" type="object" 
                            class="btn-secondary" data-hotkey="shift+v"
                            invisible="balance == 0"/>
                    <button name="refresh_lines" string="Actualiser" type="object" 
                            class="btn-secondary" data-hotkey="r"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="ref" placeholder="Référence d'allocation..." class="o_text_overflow"/>
                        </h1>
                    </div>
                    
                    <!-- Indicateurs de statut -->
                    <div class="o_payment_allocation_status">
                        <div class="alert alert-info" role="alert" invisible="balance == 0">
                            <i class="fa fa-info-circle"/> 
                            Solde à équilibrer : <field name="balance" widget="monetary"/>
                        </div>
                        <div class="alert alert-success" role="alert" invisible="balance != 0">
                            <i class="fa fa-check-circle"/> 
                            Allocation équilibrée - Prêt à valider
                        </div>
                    </div>

                    <!-- Paramètres principaux -->
                    <group name="main_params">
                        <group name="left_params" string="Paramètres">
                            <field name="partner_id" 
                                   options="{'no_create': True, 'no_open': True}"
                                   placeholder="Sélectionner un partenaire..."/>
                            <field name="show_child" 
                                   invisible="not partner_id"
                                   widget="boolean_toggle"/>
                            <field name="account_id" 
                                   options="{'no_create': True}" 
                                   domain="[('company_id', '=', company_id), ('reconcile','=', True)]"
                                   placeholder="Compte de réconciliation..."/>
                            <field name="currency_id" 
                                   options="{'no_create': True, 'no_open': True}" 
                                   groups="base.group_multi_currency"/>
                        </group>
                        
                        <group name="right_params" string="Filtres">
                            <field name="company_id" options="{'no_create': True}"/>
                            <label for="date_from" string="Période"/>
                            <div class="o_row">
                                <field name="date_from" nolabel="1" class="oe_inline" 
                                       placeholder="Du..."/>
                                <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                                <field name="date_to" nolabel="1" class="oe_inline" 
                                       placeholder="Au..."/>
                            </div>
                            <field name="max_date" readonly="1" 
                                   invisible="not max_date"
                                   string="Date max des écritures"/>
                        </group>
                    </group>

                    <!-- Champs cachés pour le contexte -->
                    <group invisible="1">
                        <field name="payment_ids"/>
                        <field name="invoice_ids"/>
                        <field name="move_line_ids"/>
                        <field name="active_move_line_ids"/>
                        <field name="manual_currency_rate"/>
                    </group>

                    <!-- Notebook avec onglets modernisés -->
                    <notebook>
                        <!-- Onglet Débits -->
                        <page string="Débits" name="debit_lines" 
                              decoration-info="len(debit_line_ids) > 0">
                            <field name="debit_line_ids" nolabel="1" 
                                   widget="section_and_note_one2many"
                                   context="{'default_type': 'debit'}">
                                <tree editable="bottom" create="false" delete="false" 
                                      decoration-success="allocate == True"
                                      decoration-muted="allocate == False">
                                    <!-- Champs cachés -->
                                    <field name="move_line_id" column_invisible="1"/>
                                    <field name="type" column_invisible="1"/>
                                    <field name="amount_residual" column_invisible="1"/>
                                    <field name="sign" column_invisible="1"/>
                                    <field name="company_currency_id" column_invisible="1"/>
                                    <field name="allocation_currency_id" column_invisible="1"/>
                                    <field name="move_currency_id" column_invisible="1"/>
                                    <field name="move_id" column_invisible="1"/>
                                    <field name="refund" column_invisible="1"/>
                                    
                                    <!-- Colonnes visibles -->
                                    <field name="document_name" string="Document" 
                                           widget="char" readonly="1"/>
                                    <field name="move_name" string="Écriture" readonly="1"/>
                                    <field name="ref" optional="show" readonly="1"/>
                                    <field name="partner_id" optional="show" readonly="1"
                                           column_invisible="not parent.show_child and parent.partner_id"/>
                                    <field name="name" optional="hide" readonly="1"/>
                                    <field name="date" optional="hide" readonly="1" widget="date"/>
                                    <field name="date_maturity" optional="show" readonly="1" widget="date"/>
                                    <field name="amount" optional="show" readonly="1" 
                                           widget="monetary" options="{'currency_field': 'move_currency_id'}"/>
                                    <field name="amount_residual_display" readonly="1" 
                                           widget="monetary" string="Restant Dû"/>
                                    <field name="allocate" widget="boolean_toggle" 
                                           options="{'autosave': False}"/>
                                    <field name="allocate_amount" sum="Total Débits" 
                                           readonly="not allocate" 
                                           widget="monetary"
                                           decoration-bf="allocate == True"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Onglet Crédits -->
                        <page string="Crédits" name="credit_lines" 
                              decoration-info="len(credit_line_ids) > 0">
                            <field name="credit_line_ids" nolabel="1" 
                                   widget="section_and_note_one2many"
                                   context="{'default_type': 'credit'}">
                                <tree editable="bottom" create="false" delete="false" 
                                      decoration-success="allocate == True"
                                      decoration-muted="allocate == False">
                                    <!-- Structure identique aux débits -->
                                    <field name="move_line_id" column_invisible="1"/>
                                    <field name="type" column_invisible="1"/>
                                    <field name="amount_residual" column_invisible="1"/>
                                    <field name="sign" column_invisible="1"/>
                                    <field name="company_currency_id" column_invisible="1"/>
                                    <field name="allocation_currency_id" column_invisible="1"/>
                                    <field name="move_currency_id" column_invisible="1"/>
                                    <field name="move_id" column_invisible="1"/>
                                    <field name="refund" column_invisible="1"/>
                                    
                                    <field name="document_name" string="Document" 
                                           widget="char" readonly="1"/>
                                    <field name="move_name" string="Écriture" readonly="1"/>
                                    <field name="ref" optional="show" readonly="1"/>
                                    <field name="partner_id" optional="show" readonly="1"
                                           column_invisible="not parent.show_child and parent.partner_id"/>
                                    <field name="name" optional="hide" readonly="1"/>
                                    <field name="date" optional="hide" readonly="1" widget="date"/>
                                    <field name="date_maturity" optional="show" readonly="1" widget="date"/>
                                    <field name="amount" optional="show" readonly="1" 
                                           widget="monetary" options="{'currency_field': 'move_currency_id'}"/>
                                    <field name="amount_residual_display" readonly="1" 
                                           widget="monetary" string="Restant Dû"/>
                                    <field name="allocate" widget="boolean_toggle" 
                                           options="{'autosave': False}"/>
                                    <field name="allocate_amount" sum="Total Crédits" 
                                           readonly="not allocate" 
                                           widget="monetary"
                                           decoration-bf="allocate == True"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Onglet Écarts d'arrondi -->
                        <page string="Écarts d'Arrondi" name="writeoff_lines" 
                              invisible="not writeoff_journal_id">
                            <group>
                                <group string="Configuration">
                                    <field name="writeoff_journal_id" 
                                           placeholder="Journal pour les écarts..."/>
                                    <field name="writeoff_ref" 
                                           placeholder="Référence de l'écart..."/>
                                </group>
                            </group>
                            
                            <field name="writeoff_line_ids" nolabel="1">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="account_id" 
                                           domain="[('deprecated', '=', False), ('company_id', '=', parent.company_id), ('account_type','not in', ['asset_receivable','liability_payable','off_balance'])]"/>
                                    <field name="name" placeholder="Libellé..."/>
                                    <field name="partner_id" optional="show"/>
                                    <field name="product_id" optional="show"/>
                                    <field name="balance" widget="monetary" sum="Total"/>
                                    <field name="analytic_distribution" widget="analytic_distribution" 
                                           optional="show" groups="analytic.group_analytic_accounting"/>
                                    <field name="tax_ids" widget="many2many_tags" optional="show"/>
                                    <field name="currency_id" column_invisible="1"/>
                                    <field name="company_id" column_invisible="1"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Onglet Écriture Compte/Partenaire -->
                        <page string="Écriture Compte/Partenaire" name="entry_lines" 
                              invisible="not create_entry">
                            <group>
                                <group string="Configuration">
                                    <field name="create_entry" widget="boolean_toggle"/>
                                    <field name="entry_journal_id" 
                                           invisible="not create_entry"
                                           placeholder="Journal pour l'écriture..."/>
                                    <field name="entry_name" 
                                           invisible="not create_entry"
                                           placeholder="Référence de l'écriture..."/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action modernisée -->
    <record id="act_account_payment_allocation_modern" model="ir.actions.act_window">
        <field name="name">Allocation de Paiement</field>
        <field name="res_model">account.payment.allocation</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_account_payment_allocation_form_modern"/>
        <field name="target">new</field>
        <field name="binding_model_id" ref="account.model_account_payment"/>
        <field name="binding_type">action</field>
    </record>
</odoo>
