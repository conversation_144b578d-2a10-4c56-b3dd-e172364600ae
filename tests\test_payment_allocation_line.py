"""
Tests unitaires pour les lignes d'allocation de paiement
"""

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from odoo.tools import float_compare
import logging

_logger = logging.getLogger(__name__)


class TestPaymentAllocationLine(TransactionCase):
    """
    Tests pour le modèle account.payment.allocation.line
    """
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Configuration de base
        cls.company = cls.env.ref('base.main_company')
        cls.currency_eur = cls.env.ref('base.EUR')
        cls.currency_usd = cls.env.ref('base.USD')
        
        # Compte comptable
        cls.account_receivable = cls.env['account.account'].create({
            'name': 'Test Receivable',
            'code': 'TEST_REC',
            'account_type': 'asset_receivable',
            'reconcile': True,
            'company_id': cls.company.id,
        })
        
        cls.account_expense = cls.env['account.account'].create({
            'name': 'Test Expense',
            'code': 'TEST_EXP',
            'account_type': 'expense',
            'company_id': cls.company.id,
        })
        
        # Journal
        cls.bank_journal = cls.env['account.journal'].create({
            'name': 'Test Bank',
            'code': 'TBANK',
            'type': 'bank',
            'company_id': cls.company.id,
        })
        
        # Partenaire
        cls.partner = cls.env['res.partner'].create({
            'name': 'Test Partner',
            'is_company': True,
            'customer_rank': 1,
        })
        
        # Allocation de base
        cls.allocation = cls.env['account.payment.allocation'].create({
            'account_id': cls.account_receivable.id,
            'partner_id': cls.partner.id,
            'currency_id': cls.currency_eur.id,
        })
    
    def _create_test_move_line(self, debit=0.0, credit=0.0, currency=None):
        """Crée une écriture comptable de test"""
        currency = currency or self.currency_eur
        
        move = self.env['account.move'].create({
            'move_type': 'entry',
            'date': '2023-01-01',
            'journal_id': self.bank_journal.id,
            'line_ids': [
                (0, 0, {
                    'account_id': self.account_receivable.id,
                    'partner_id': self.partner.id,
                    'debit': debit,
                    'credit': credit,
                    'currency_id': currency.id if currency != self.company.currency_id else False,
                    'amount_currency': debit - credit if currency != self.company.currency_id else 0,
                }),
                (0, 0, {
                    'account_id': self.account_expense.id,
                    'debit': credit,
                    'credit': debit,
                    'currency_id': currency.id if currency != self.company.currency_id else False,
                    'amount_currency': credit - debit if currency != self.company.currency_id else 0,
                }),
            ],
        })
        move.action_post()
        
        return move.line_ids.filtered(lambda l: l.account_id == self.account_receivable)[0]
    
    def test_line_creation_debit(self):
        """Test la création d'une ligne de débit"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
        })
        
        self.assertEqual(line.type, 'debit')
        self.assertEqual(line.sign, 1)
        self.assertEqual(line.move_line_id, move_line)
        self.assertEqual(line.partner_id, self.partner)
    
    def test_line_creation_credit(self):
        """Test la création d'une ligne de crédit"""
        move_line = self._create_test_move_line(credit=800.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'credit',
        })
        
        self.assertEqual(line.type, 'credit')
        self.assertEqual(line.sign, -1)
        self.assertEqual(line.move_line_id, move_line)
    
    def test_amount_residual_calculation(self):
        """Test le calcul du montant résiduel"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
        })
        
        # Le montant résiduel devrait être égal au débit
        self.assertEqual(float_compare(line.amount_residual, 1000.0, precision_digits=2), 0)
        self.assertEqual(float_compare(line.amount_residual_display, 1000.0, precision_digits=2), 0)
    
    def test_amount_residual_display_credit(self):
        """Test l'affichage du montant résiduel pour un crédit"""
        move_line = self._create_test_move_line(credit=800.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'credit',
        })
        
        # Pour un crédit, l'affichage devrait être positif
        self.assertEqual(float_compare(line.amount_residual_display, 800.0, precision_digits=2), 0)
    
    def test_currency_conversion(self):
        """Test la conversion de devise"""
        # Configure un taux de change
        self.env['res.currency.rate'].create({
            'currency_id': self.currency_usd.id,
            'rate': 0.85,  # 1 USD = 0.85 EUR
            'name': '2023-01-01',
            'company_id': self.company.id,
        })
        
        # Crée une écriture en USD
        move_line = self._create_test_move_line(debit=1000.0, currency=self.currency_usd)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
        })
        
        # Le montant résiduel devrait être converti en EUR
        # 1000 USD * 0.85 = 850 EUR (approximativement)
        self.assertAlmostEqual(line.amount_residual, 850.0, delta=10.0)
    
    def test_onchange_allocate_true(self):
        """Test l'onchange quand allocate passe à True"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': False,
        })
        
        # Active l'allocation
        line.allocate = True
        line._onchange_allocate()
        
        # Le montant d'allocation devrait être défini automatiquement
        self.assertEqual(float_compare(line.allocate_amount, 1000.0, precision_digits=2), 0)
    
    def test_onchange_allocate_false(self):
        """Test l'onchange quand allocate passe à False"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': True,
            'allocate_amount': 500.0,
        })
        
        # Désactive l'allocation
        line.allocate = False
        line._onchange_allocate()
        
        # Le montant d'allocation devrait être remis à zéro
        self.assertEqual(float_compare(line.allocate_amount, 0.0, precision_digits=2), 0)
    
    def test_optimal_allocation_with_counterpart(self):
        """Test l'allocation optimale avec contrepartie"""
        # Crée une ligne de débit
        debit_move_line = self._create_test_move_line(debit=1000.0)
        debit_line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': debit_move_line.id,
            'type': 'debit',
        })
        
        # Crée une ligne de crédit plus petite
        credit_move_line = self._create_test_move_line(credit=600.0)
        credit_line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': credit_move_line.id,
            'type': 'credit',
        })
        
        # Active d'abord le crédit
        credit_line.allocate = True
        credit_line._onchange_allocate()
        
        # Puis active le débit
        debit_line.allocate = True
        debit_line._onchange_allocate()
        
        # Le débit devrait être limité par le crédit disponible
        self.assertEqual(float_compare(debit_line.allocate_amount, 600.0, precision_digits=2), 0)
    
    def test_constraint_allocate_amount_exceeds_residual(self):
        """Test la contrainte sur le montant alloué qui dépasse le résiduel"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': True,
            'allocate_amount': 1500.0,  # Dépasse le résiduel
        })
        
        with self.assertRaises(ValidationError):
            line._check_allocate_amount()
    
    def test_allocate_maximum_method(self):
        """Test la méthode allocate_maximum"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': False,
        })
        
        # Appelle la méthode
        result = line.allocate_maximum()
        
        self.assertTrue(result)
        self.assertTrue(line.allocate)
        self.assertEqual(float_compare(line.allocate_amount, 1000.0, precision_digits=2), 0)
    
    def test_clear_allocation_method(self):
        """Test la méthode clear_allocation"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': True,
            'allocate_amount': 500.0,
        })
        
        # Appelle la méthode
        result = line.clear_allocation()
        
        self.assertTrue(result)
        self.assertFalse(line.allocate)
        self.assertEqual(float_compare(line.allocate_amount, 0.0, precision_digits=2), 0)
    
    def test_get_allocation_info_method(self):
        """Test la méthode get_allocation_info"""
        move_line = self._create_test_move_line(debit=1000.0)
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': move_line.id,
            'type': 'debit',
            'allocate': True,
            'allocate_amount': 750.0,
        })
        
        info = line.get_allocation_info()
        
        self.assertIsInstance(info, dict)
        self.assertEqual(info['type'], 'debit')
        self.assertEqual(info['allocate'], True)
        self.assertEqual(float_compare(info['allocate_amount'], 750.0, precision_digits=2), 0)
        self.assertEqual(info['currency'], self.currency_eur.name)
    
    def test_document_name_computation_payment(self):
        """Test le calcul du nom de document pour un paiement"""
        # Crée un paiement
        payment = self.env['account.payment'].create({
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner.id,
            'amount': 1000.0,
            'currency_id': self.currency_eur.id,
            'journal_id': self.bank_journal.id,
        })
        payment.action_post()
        
        # Récupère la ligne de paiement
        payment_line = payment.line_ids.filtered(lambda l: l.account_id == self.account_receivable)[0]
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': payment_line.id,
            'type': 'credit',
        })
        
        # Le nom du document devrait être celui du paiement
        self.assertTrue(payment.name in line.document_name)
    
    def test_document_name_computation_invoice(self):
        """Test le calcul du nom de document pour une facture"""
        # Crée une facture
        invoice = self.env['account.move'].create({
            'move_type': 'out_invoice',
            'partner_id': self.partner.id,
            'currency_id': self.currency_eur.id,
            'invoice_line_ids': [(0, 0, {
                'name': 'Test Product',
                'quantity': 1,
                'price_unit': 1000.0,
                'account_id': self.account_expense.id,
            })],
        })
        invoice.action_post()
        
        # Récupère la ligne de facture
        invoice_line = invoice.line_ids.filtered(lambda l: l.account_id == self.account_receivable)[0]
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': invoice_line.id,
            'type': 'debit',
        })
        
        # Le nom du document devrait être celui de la facture
        self.assertTrue(invoice.name in line.document_name)
    
    def test_refund_detection(self):
        """Test la détection des avoirs"""
        # Crée un avoir
        refund = self.env['account.move'].create({
            'move_type': 'out_refund',
            'partner_id': self.partner.id,
            'currency_id': self.currency_eur.id,
            'invoice_line_ids': [(0, 0, {
                'name': 'Test Refund',
                'quantity': 1,
                'price_unit': 500.0,
                'account_id': self.account_expense.id,
            })],
        })
        refund.action_post()
        
        # Récupère la ligne d'avoir
        refund_line = refund.line_ids.filtered(lambda l: l.account_id == self.account_receivable)[0]
        
        line = self.env['account.payment.allocation.line'].create({
            'allocation_id': self.allocation.id,
            'move_line_id': refund_line.id,
            'type': 'credit',
        })
        
        # Devrait être détecté comme un avoir
        self.assertTrue(line.refund)
