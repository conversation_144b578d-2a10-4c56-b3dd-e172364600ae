"""
Modèle de ligne d'allocation modernisé pour Odoo 17.0
Code refactorisé selon les bonnes pratiques
"""

from odoo import models, fields, api, _
from odoo.tools import float_compare, float_is_zero
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class PaymentAllocationLineModern(models.TransientModel):
    """
    Ligne d'allocation de paiement modernisée
    """
    _name = "account.payment.allocation.line"
    _description = 'Ligne d\'Allocation de Paiement'
    _check_company_auto = True
    _order = 'type, date_maturity, date, move_name, id'

    # ========== CHAMPS DE BASE ==========
    
    allocation_id = fields.Many2one(
        'account.payment.allocation',
        string="Allocation",
        required=True,
        ondelete='cascade',
        index=True
    )
    
    type = fields.Selection([
        ('debit', 'Débit'),
        ('credit', 'Crédit')
    ], string="Type", required=True, index=True)
    
    move_line_id = fields.Many2one(
        'account.move.line',
        string="Écriture comptable",
        required=True,
        ondelete='cascade',
        index=True
    )
    
    # ========== CHAMPS DE DEVISE ==========
    
    company_currency_id = fields.Many2one(
        related='move_line_id.company_currency_id',
        string="Devise société",
        store=True
    )
    
    move_currency_id = fields.Many2one(
        'res.currency',
        string="Devise écriture",
        compute='_compute_move_currency_id',
        store=True
    )
    
    allocation_currency_id = fields.Many2one(
        related='allocation_id.currency_id',
        string="Devise allocation",
        store=True
    )
    
    # ========== CHAMPS D'ALLOCATION ==========
    
    allocate = fields.Boolean(
        string="Allouer",
        default=False,
        help="Cochez pour inclure cette ligne dans l'allocation"
    )
    
    allocate_amount = fields.Monetary(
        string="Montant alloué",
        currency_field='allocation_currency_id',
        help="Montant à allouer pour cette ligne"
    )
    
    # ========== CHAMPS CALCULÉS ==========
    
    amount_residual = fields.Monetary(
        string="Montant résiduel",
        compute='_compute_amount_residual',
        store=False,
        currency_field='allocation_currency_id'
    )
    
    amount_residual_display = fields.Monetary(
        string="Montant non alloué",
        compute='_compute_amount_residual_display',
        store=False,
        currency_field='allocation_currency_id',
        help="Montant restant à allouer"
    )
    
    amount = fields.Monetary(
        string="Montant",
        compute='_compute_amount',
        store=False,
        currency_field='move_currency_id'
    )
    
    sign = fields.Integer(
        string="Signe",
        compute='_compute_sign',
        store=True,
        help="1 pour débit, -1 pour crédit"
    )
    
    # ========== CHAMPS LIÉS ==========
    
    payment_id = fields.Many2one(
        related='move_line_id.payment_id',
        string="Paiement",
        readonly=True,
        store=True
    )
    
    move_id = fields.Many2one(
        related='move_line_id.move_id',
        string="Écriture",
        readonly=True,
        store=True
    )
    
    partner_id = fields.Many2one(
        related='move_line_id.partner_id',
        string="Partenaire",
        readonly=True,
        store=True
    )
    
    account_id = fields.Many2one(
        related='move_line_id.account_id',
        string="Compte",
        readonly=True,
        store=True
    )
    
    # ========== CHAMPS D'AFFICHAGE ==========
    
    move_name = fields.Char(
        related='move_line_id.move_id.name',
        string="N° écriture",
        readonly=True,
        store=True
    )
    
    ref = fields.Char(
        related='move_line_id.ref',
        string="Référence",
        readonly=True,
        store=True
    )
    
    name = fields.Char(
        related='move_line_id.name',
        string="Libellé",
        readonly=True,
        store=True
    )
    
    date = fields.Date(
        related='move_line_id.date',
        string="Date",
        readonly=True,
        store=True
    )
    
    date_maturity = fields.Date(
        related='move_line_id.date_maturity',
        string="Date d'échéance",
        readonly=True,
        store=True
    )
    
    balance = fields.Monetary(
        related='move_line_id.amount_currency',
        string="Solde",
        currency_field='move_currency_id',
        readonly=True
    )
    
    document_name = fields.Char(
        string="Document",
        compute='_compute_document_name',
        store=False
    )
    
    refund = fields.Boolean(
        string="Avoir",
        compute='_compute_refund',
        store=True
    )
    
    # ========== MÉTHODES CALCULÉES ==========
    
    @api.depends('move_line_id.currency_id', 'move_line_id.company_currency_id')
    def _compute_move_currency_id(self):
        """Calcule la devise de l'écriture"""
        for record in self:
            record.move_currency_id = (
                record.move_line_id.currency_id or 
                record.move_line_id.company_currency_id
            )
    
    @api.depends('type')
    def _compute_sign(self):
        """Calcule le signe selon le type"""
        for record in self:
            record.sign = 1 if record.type == 'debit' else -1
    
    @api.depends('sign', 'balance')
    def _compute_amount(self):
        """Calcule le montant avec le bon signe"""
        for record in self:
            record.amount = record.balance * record.sign
    
    @api.depends('move_line_id', 'allocation_currency_id', 'allocation_id.max_date')
    def _compute_amount_residual(self):
        """Calcule le montant résiduel dans la devise d'allocation"""
        for record in self:
            if not record.move_line_id or not record.allocation_currency_id:
                record.amount_residual = 0.0
                continue
            
            max_date = record.allocation_id.max_date or fields.Date.today()
            
            # Optimisation: utilise les champs cached
            if record.allocation_currency_id == record.company_currency_id:
                record.amount_residual = record.move_line_id.amount_residual
            elif record.allocation_currency_id == record.move_currency_id:
                record.amount_residual = record.move_line_id.amount_residual_currency
            elif record.move_line_id.currency_id:
                # Conversion depuis la devise de l'écriture
                record.amount_residual = record.move_currency_id._convert(
                    record.move_line_id.amount_residual_currency,
                    record.allocation_currency_id,
                    record.allocation_id.company_id,
                    max_date
                )
            else:
                # Conversion depuis la devise de la société
                record.amount_residual = record.company_currency_id._convert(
                    record.move_line_id.amount_residual,
                    record.allocation_currency_id,
                    record.allocation_id.company_id,
                    max_date
                )
    
    @api.depends('amount_residual', 'sign')
    def _compute_amount_residual_display(self):
        """Calcule le montant résiduel d'affichage avec le bon signe"""
        for record in self:
            record.amount_residual_display = record.amount_residual * record.sign
    
    @api.depends('payment_id', 'move_id')
    def _compute_document_name(self):
        """Calcule le nom du document"""
        for record in self:
            if record.payment_id:
                record.document_name = record.payment_id.display_name
            elif record.move_id:
                record.document_name = record.move_id.display_name
            else:
                record.document_name = _("Écriture comptable")
    
    @api.depends('move_id.move_type')
    def _compute_refund(self):
        """Détermine si c'est un avoir"""
        for record in self:
            record.refund = record.move_id.move_type in ['out_refund', 'in_refund']
    
    # ========== MÉTHODES ONCHANGE ==========
    
    @api.onchange('allocate', 'amount_residual_display')
    def _onchange_allocate(self):
        """Calcule automatiquement le montant d'allocation"""
        if not self.allocate:
            self.allocate_amount = 0.0
            return
        
        # Calcule le montant optimal d'allocation
        self._compute_optimal_allocation_amount()
    
    def _compute_optimal_allocation_amount(self):
        """Calcule le montant optimal d'allocation"""
        if not self.allocation_id:
            self.allocate_amount = abs(self.amount_residual_display)
            return
        
        # Récupère les autres lignes allouées
        other_lines = (
            self.allocation_id.debit_line_ids + 
            self.allocation_id.credit_line_ids
        ).filtered(lambda l: l != self and l.allocate)
        
        # Calcule le total des autres allocations
        other_total = sum(line.allocate_amount * line.sign for line in other_lines)
        
        # Calcule le montant disponible pour cette ligne
        if self.sign > 0:  # Débit
            # Pour un débit, on peut allouer jusqu'au total des crédits
            available = max(0, -other_total)
        else:  # Crédit
            # Pour un crédit, on peut allouer jusqu'au total des débits
            available = max(0, other_total)
        
        # Le montant d'allocation est le minimum entre disponible et résiduel
        max_allocatable = abs(self.amount_residual_display)
        self.allocate_amount = min(available, max_allocatable) if available > 0 else max_allocatable
    
    @api.onchange('allocate_amount')
    def _onchange_allocate_amount(self):
        """Déclenche le recalcul du solde de l'allocation"""
        if self.allocation_id:
            self.allocation_id._compute_balance()
    
    # ========== CONTRAINTES ==========
    
    @api.constrains('allocate_amount', 'amount_residual_display')
    def _check_allocate_amount(self):
        """Vérifie que le montant alloué ne dépasse pas le résiduel"""
        for record in self:
            if record.allocate and record.allocate_amount:
                max_amount = abs(record.amount_residual_display)
                if float_compare(
                    record.allocate_amount, 
                    max_amount, 
                    precision_digits=2
                ) > 0:
                    raise ValidationError(
                        _("Le montant alloué (%(allocated)s) ne peut pas dépasser "
                          "le montant résiduel (%(residual)s) pour la ligne %(line)s") % {
                            'allocated': record.allocate_amount,
                            'residual': max_amount,
                            'line': record.document_name
                        }
                    )
    
    # ========== MÉTHODES UTILITAIRES ==========
    
    def allocate_maximum(self):
        """Alloue le montant maximum possible"""
        self.ensure_one()
        if not self.allocate:
            self.allocate = True
        self._compute_optimal_allocation_amount()
        return True
    
    def clear_allocation(self):
        """Efface l'allocation"""
        self.ensure_one()
        self.allocate = False
        self.allocate_amount = 0.0
        return True
    
    def get_allocation_info(self):
        """Retourne les informations d'allocation pour l'interface"""
        self.ensure_one()
        return {
            'id': self.id,
            'document_name': self.document_name,
            'amount_residual': self.amount_residual_display,
            'allocate_amount': self.allocate_amount,
            'allocate': self.allocate,
            'currency': self.allocation_currency_id.name,
            'type': self.type,
            'partner': self.partner_id.name if self.partner_id else '',
            'date_maturity': self.date_maturity,
        }
